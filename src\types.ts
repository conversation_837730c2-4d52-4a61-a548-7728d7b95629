export interface Credentials {
  organization: string;
  pat: string;
}

export interface Project {
    id: string;
    name: string;
}

export interface Team {
    id: string;
    name: string;
}

export interface DashboardConfig {
    project: Project;
    teams: Team[];
}


export interface ChartData {
  name: string;
  [key: string]: string | number;
}

export interface TestPlan {
  id: number;
  name: string;
  iteration: string;
  startDate: string;
  endDate: string;
  state: string;
  rootSuite: {
    id: string;
    name: string;
  };
}

export interface TestSuite {
  id: number;
  name: string;
  testCaseCount: number;
  suiteType: string;
  state: string;
  lastUpdatedDate: string;
}

export interface TestPoint {
  id: number;
  outcome: string;
  state: string;
  testCase: {
    id: string;
  };
  lastUpdatedDate: string;
  assignedTo?: {
    id: string;
    displayName: string;
  };
}

export interface SprintTestCoverage {
  sprintName: string;
  sprintPath: string;
  startDate: Date;
  endDate: Date;
  testMetrics: {
    totalPlanned: number;
    totalExecuted: number;
    passed: number;
    failed: number;
    blocked: number;
    notRun: number;
    coverage: number;
    passRate: number;
  };
}

export enum MetricTab {
  Overview = 'Project Overview',
  StoryPointAcceptance = 'Story Acceptance',
  SprintVelocityByMonth = 'Monthly Velocity',
  SprintVelocityBySprint = 'Sprint Velocity',
  DefectMetrics = 'Defect Metrics',
  TestMetrics = 'Test Metrics',
  BacklogHealth = 'Backlog Health',
}

export interface TabProps {
  data: Record<string, any>;
  teams: Team[];
  project: Project;
}

export type KpiStatus = 'good' | 'warning' | 'danger' | 'neutral';