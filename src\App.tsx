import React, { useState, useCallback, useEffect } from 'react';
import { Dashboard } from './components/Dashboard';
import { Login } from './components/Login';
import { Setup } from './components/Setup';
import type { Credentials, DashboardConfig } from './types';
import { AzureDevOpsService } from './services/api';

const CREDENTIALS_KEY = 'azureDevOpsCredentials';
const CONFIG_KEY = 'azureDevOpsConfig';

const App: React.FC = () => {
  const [credentials, setCredentials] = useState<Credentials | null>(null);
  const [config, setConfig] = useState<DashboardConfig | null>(null);
  const [isConnecting, setIsConnecting] = useState(false);
  const [loginError, setLoginError] = useState<string | null>(null);
  const [isVerifying, setIsVerifying] = useState(true);

  useEffect(() => {
    const verifyStoredCredentials = async () => {
      const savedCreds = localStorage.getItem(CREDENTIALS_KEY);
      const savedConfig = localStorage.getItem(CONFIG_KEY);

      if (savedCreds && savedConfig) {
        try {
          const parsedCreds: Credentials = JSON.parse(savedCreds);
          const parsedConfig: DashboardConfig = JSON.parse(savedConfig);
          const apiService = new AzureDevOpsService(parsedCreds.organization, parsedCreds.pat);
          await apiService.verifyConnection();
          
          setCredentials(parsedCreds);
          setConfig(parsedConfig);
        } catch (e) {
          localStorage.removeItem(CREDENTIALS_KEY);
          localStorage.removeItem(CONFIG_KEY);
          setCredentials(null);
          setConfig(null);
        }
      }
      setIsVerifying(false);
    };

    verifyStoredCredentials();
  }, []);

  const handleLogin = useCallback(async (creds: Credentials) => {
    setIsConnecting(true);
    setLoginError(null);
    try {
      const apiService = new AzureDevOpsService(creds.organization, creds.pat);
      await apiService.verifyConnection();
      setCredentials(creds);
      localStorage.setItem(CREDENTIALS_KEY, JSON.stringify(creds));
    } catch (e) {
      const errorMessage = e instanceof Error ? e.message : 'An unknown error occurred during connection.';
      setLoginError(errorMessage);
    } finally {
      setIsConnecting(false);
    }
  }, []);

  const handleConfigured = useCallback((newConfig: DashboardConfig) => {
    localStorage.setItem(CONFIG_KEY, JSON.stringify(newConfig));
    setConfig(newConfig);
  }, []);

  const handleLogout = useCallback(() => {
    setCredentials(null);
    setConfig(null);
    setLoginError(null);
    localStorage.removeItem(CREDENTIALS_KEY);
    localStorage.removeItem(CONFIG_KEY);
  }, []);

  const handleChangeConfig = useCallback(() => {
    setConfig(null);
    localStorage.removeItem(CONFIG_KEY);
  }, []);

  const renderContent = () => {
    if (isVerifying) {
        return (
            <div className="flex flex-col items-center justify-center h-screen text-lg text-slate-400">
                <svg className="animate-spin h-10 w-10 text-sky-500 mb-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                <span>Verifying session...</span>
            </div>
        );
    }

    if (!credentials) {
      return <Login onLogin={handleLogin} isConnecting={isConnecting} error={loginError} />;
    }
    if (!config) {
      return <Setup credentials={credentials} onConfigured={handleConfigured} onLogout={handleLogout} />;
    }
    return <Dashboard credentials={credentials} config={config} onLogout={handleLogout} onBack={handleChangeConfig} />;
  };

  return (
    <div className="min-h-screen font-sans text-slate-200 bg-gradient-to-br from-slate-900 to-gray-800">
        {renderContent()}
    </div>
  );
};

export default App;