import React from 'react';
import type { KpiStatus } from '../../types';

interface KpiCardProps {
  title: string;
  value: string | number;
  icon: React.ReactNode;
  status?: KpiStatus;
}

const statusStyles = {
  good: { border: 'border-l-green-500', bg: 'bg-green-500/10', iconBg: 'bg-green-500/20', text: 'text-green-400', shadow: 'hover:shadow-green-500/20' },
  warning: { border: 'border-l-orange-500', bg: 'bg-orange-500/10', iconBg: 'bg-orange-500/20', text: 'text-orange-400', shadow: 'hover:shadow-orange-500/20' },
  danger: { border: 'border-l-red-500', bg: 'bg-red-500/10', iconBg: 'bg-red-500/20', text: 'text-red-400', shadow: 'hover:shadow-red-500/20' },
  neutral: { border: 'border-l-sky-500', bg: 'bg-slate-800/50', iconBg: 'bg-slate-700/50', text: 'text-sky-400', shadow: 'hover:shadow-sky-500/20' },
};

export const KpiCard: React.FC<KpiCardProps> = ({ title, value, icon, status = 'neutral' }) => {
  const styles = statusStyles[status];
  
  const coloredIcon = React.isValidElement(icon) 
    ? React.cloneElement(icon as React.ReactElement<any>, { className: `w-8 h-8 ${styles.text}` }) 
    : icon;

  return (
    <div className={`backdrop-blur-sm p-5 shadow-lg rounded-lg border border-slate-700 flex items-center space-x-4 transition-all duration-300 hover:bg-slate-800/80 border-l-4 ${styles.border} ${styles.bg} hover:scale-[1.03] hover:shadow-xl ${styles.shadow}`}>
      <div className={`flex-shrink-0 w-16 h-16 flex items-center justify-center rounded-full ${styles.iconBg}`}>
        {coloredIcon}
      </div>
      <div>
        <div className="text-xs font-semibold text-slate-400 uppercase tracking-wider">{title}</div>
        <div className="text-4xl font-bold text-slate-100">{value}</div>
      </div>
    </div>
  );
};