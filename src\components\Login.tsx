import React, { useState } from 'react';
import type { Credentials } from '../types';

interface LoginProps {
  onLogin: (credentials: Credentials) => void;
  isConnecting: boolean;
  error: string | null;
}

const InfoTooltip: React.FC<{ text: string }> = ({ text }) => {
    const [show, setShow] = useState(false);
    return (
        <div className="relative flex items-center ml-2" onMouseEnter={() => setShow(true)} onMouseLeave={() => setShow(false)}>
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-slate-400 cursor-pointer" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={2} aria-label="More info">
                <path strokeLinecap="round" strokeLinejoin="round" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            {show && (
                <div role="tooltip" className="absolute bottom-full left-1/2 -translate-x-1/2 mb-2 w-64 p-2 bg-slate-900 text-slate-300 text-xs rounded-md shadow-lg z-10 border border-slate-700">
                    {text}
                </div>
            )}
        </div>
    );
};

const InputField: React.FC<{ id: string, label: string, type?: string, value: string, onChange: (e: React.ChangeEvent<HTMLInputElement>) => void, placeholder: string, children?: React.ReactNode }> = ({ id, label, type = 'text', value, onChange, placeholder, children }) => (
    <div>
        <div className="flex items-center">
            <label htmlFor={id} className="block text-sm font-medium text-slate-300">
                {label}
            </label>
            {children}
        </div>
        <div className="mt-1">
            <input
                type={type}
                name={id}
                id={id}
                className="block w-full px-3 py-2 bg-slate-700 border border-slate-600 rounded-md shadow-sm placeholder-slate-400 text-slate-200 focus:outline-none focus:ring-sky-500 focus:border-sky-500 sm:text-sm"
                value={value}
                onChange={onChange}
                placeholder={placeholder}
                required
                aria-required="true"
            />
        </div>
    </div>
);


export const Login: React.FC<LoginProps> = ({ onLogin, isConnecting, error }) => {
  const [organization, setOrganization] = useState('');
  const [pat, setPat] = useState('');
  const [showHelp, setShowHelp] = useState(false);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (organization && pat) {
      onLogin({ organization, pat });
    }
  };

  return (
    <div className="flex items-center justify-center min-h-screen p-4">
      <div className="w-full max-w-md p-8 space-y-8 bg-slate-800 rounded-2xl shadow-2xl shadow-slate-900/50">
        <div>
           <svg className="w-16 h-16 mx-auto text-sky-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path></svg>
          <h2 className="mt-6 text-3xl font-extrabold text-center text-slate-100">
            Connect to Azure DevOps
          </h2>
          <p className="mt-2 text-sm text-center text-slate-400">
            Enter your credentials to visualize your project metrics.
          </p>
        </div>
        <form className="mt-8 space-y-6" onSubmit={handleSubmit}>
          <div className="space-y-4 rounded-md">
            <InputField 
                id="organization" 
                label="Organization" 
                value={organization} 
                onChange={(e) => setOrganization(e.target.value)}
                placeholder="your-organization-name"
            >
                <InfoTooltip text="This is the name of your Azure DevOps organization. It's the part of the URL before '.dev.azure.com'." />
            </InputField>
            <InputField 
                id="pat" 
                label="Personal Access Token (PAT)" 
                type="password" 
                value={pat} 
                onChange={(e) => setPat(e.target.value)}
                placeholder="paste-your-pat-here"
            >
                <InfoTooltip text="A Personal Access Token is used to securely access your data. It needs 'Read' access for Work Items, Project and Team, and Test Management scopes." />
            </InputField>
          </div>
          
          {error && (
              <div className="p-3 text-sm text-red-300 bg-red-900/40 border border-red-500/30 rounded-md" role="alert">
                  <p className="font-bold">Connection Failed</p>
                  <p className="text-xs mt-1">{error}</p>
              </div>
          )}

          <div className="text-sm">
              <button type="button" onClick={() => setShowHelp(!showHelp)} className="font-medium text-sky-500 hover:text-sky-400 transition" aria-expanded={showHelp}>
                  Need help finding these?
              </button>
          </div>

          {showHelp && (
              <div className="p-4 text-xs bg-slate-900/50 border border-slate-700 rounded-md space-y-3 text-slate-300 transition-all duration-300">
                  <p><strong>Organization:</strong> Your organization name is in your Azure DevOps URL. Example: `https://dev.azure.com/your-organization-name`.</p>
                  <p><strong>Personal Access Token (PAT):</strong></p>
                  <ol className="list-decimal list-inside space-y-1 pl-2">
                      <li>Log in to your Azure DevOps organization.</li>
                      <li>Go to <strong>User settings</strong> &gt; <strong>Personal Access Tokens</strong>.</li>
                      <li>Click <strong>+ New Token</strong>.</li>
                      <li>Give it a name, select an expiration date, and set the scopes.</li>
                      <li>You must grant 'Read' access for: <strong>Work Items</strong>, <strong>Project and Team</strong>, and <strong>Test Management</strong>.</li>
                      <li>Click <strong>Create</strong> and copy the token immediately. You won't see it again.</li>
                  </ol>
              </div>
          )}

          <div>
            <button
              type="submit"
              disabled={isConnecting || !organization || !pat}
              className="relative flex justify-center w-full px-4 py-2 text-sm font-medium text-white bg-sky-600 border border-transparent rounded-md group hover:bg-sky-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-slate-800 focus:ring-sky-500 transition duration-150 ease-in-out disabled:bg-slate-600 disabled:cursor-not-allowed"
            >
                {isConnecting && (
                    <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" aria-hidden="true">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                )}
              <span aria-live="polite">
                {isConnecting ? 'Connecting...' : 'Connect & View Dashboard'}
              </span>
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};