# Vercel Frontend Deployment Guide

## 🚀 Deploy to Vercel (Frontend Method)

### Step 1: Prepare Your Project
Your project is already configured for Vercel deployment with:
- ✅ `vercel.json` configuration file
- ✅ Optimized build settings
- ✅ Environment variable support
- ✅ SPA routing configured

### Step 2: Deploy via Vercel Dashboard

1. **Go to [Vercel Dashboard](https://vercel.com/dashboard)**
2. **Click "New Project"**
3. **Import your Git repository** (or upload the project folder)
4. **Configure the project:**
   - **Framework Preset:** Vite
   - **Root Directory:** `./` (leave as default)
   - **Build Command:** `npm run build` (auto-detected)
   - **Output Directory:** `dist` (auto-detected)

### Step 3: Set Environment Variables

In the Vercel dashboard, go to your project settings and add:

```
# Optional: Only if you have AI features using Gemini
GEMINI_API_KEY=your_gemini_api_key_here
```

**Note:** The main authentication is handled through Azure DevOps PAT tokens entered by users in the application interface.

### Step 4: Deploy

Click "Deploy" and Vercel will:
- Install dependencies
- Run the build command
- Deploy to a `.vercel.app` domain
- Set up automatic deployments on git pushes

## 📋 Vercel Configuration Details

Your `vercel.json` includes:
- **SPA Routing:** All routes redirect to `index.html`
- **Asset Caching:** Static assets cached for 1 year
- **Security Headers:** XSS protection and frame options
- **Build Optimization:** Code splitting and minification

## 🔧 Post-Deployment

After deployment, you can:
- **Add a custom domain** in Vercel dashboard
- **Configure analytics** and monitoring
- **Set up preview deployments** for pull requests
- **Manage environment variables** per deployment environment

## 🌐 Live URL

Once deployed, you'll get a URL like:
`https://azure-devops-metrics-dashboard.vercel.app`

That's it! Your dashboard is now live and production-ready! 🎉