<div align="center">
<img width="1200" height="475" alt="GHBanner" src="https://github.com/user-attachments/assets/0aa67016-6eaf-458a-adb2-6e31a0763ed6" />
</div>

# Run and deploy your AI Studio app

This contains everything you need to run your app locally.

View your app in AI Studio: https://ai.studio/apps/drive/1XxlSaEkKy3jsUvV84fpzDgWNtjhG7swP

# Azure DevOps Metrics Dashboard

A modern, production-ready dashboard for Azure DevOps metrics built with React, TypeScript, and Vite.

## Features

- 📊 Real-time Azure DevOps metrics visualization
- 🔐 Secure authentication with Azure DevOps PAT
- 📈 Interactive charts using Recharts
- 🎨 Modern UI with Tailwind CSS
- 🚀 Optimized production build
- 🐳 Docker containerization ready

## Prerequisites

- Node.js 18+
- Azure DevOps Personal Access Token (PAT)

## Local Development

1. **Install dependencies:**
   ```bash
   npm install
   ```

2. **Set up environment variables:**
   ```bash
   cp .env.production.example .env.local
   # Edit .env.local and add your GEMINI_API_KEY
   ```

3. **Run development server:**
   ```bash
   npm run dev
   ```

## Production Deployment

### Option 1: Docker Deployment

1. **Build and run with Docker Compose:**
   ```bash
   docker-compose up --build
   ```

2. **Or build and run manually:**
   ```bash
   docker build -t azure-devops-dashboard .
   docker run -p 3000:80 azure-devops-dashboard
   ```

### Option 2: Static Hosting

1. **Build the application:**
   ```bash
   npm run build
   ```

2. **Serve the `dist` folder:**
   ```bash
   npm run serve
   ```

3. **Deploy to any static hosting service:**
   - Netlify
   - Vercel
   - GitHub Pages
   - AWS S3 + CloudFront

### Option 3: Vercel (Frontend Deployment)

1. **Push to Git repository** (GitHub, GitLab, or Bitbucket)
2. **Go to [Vercel Dashboard](https://vercel.com/dashboard)**
3. **Import your repository**
4. **Vercel will auto-detect Vite settings**
5. **Add environment variables in Vercel dashboard**
6. **Deploy!**

See [VERCEL_DEPLOYMENT.md](VERCEL_DEPLOYMENT.md) for detailed instructions.

## Environment Variables

Create a `.env.production` file for production:

```env
GEMINI_API_KEY=your_production_api_key_here
```

## Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run preview` - Preview production build locally
- `npm run serve` - Serve production build
- `npm run lint` - Type check with TypeScript
- `npm run clean` - Clean build directory

## Project Structure

```
src/
├── components/     # React components
├── services/       # API services
├── types.ts        # TypeScript type definitions
├── constants.ts    # Application constants
├── App.tsx         # Main application component
└── index.tsx       # Application entry point
```

## Security Notes

- Never commit API keys to version control
- Use environment variables for sensitive data
- The app stores credentials in localStorage (consider implementing server-side session management for production)
