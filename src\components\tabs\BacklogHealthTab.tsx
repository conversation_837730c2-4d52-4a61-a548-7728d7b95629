import React from 'react';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>Axi<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON> } from 'recharts';
import { ChartCard } from '../ChartCard';
import { KpiCard } from '../common/KpiCard';
import { CustomTooltip } from './CustomTooltip';
import type { TabProps, KpiStatus } from '../../types';
import { CHART_COLORS, KPI_ICONS } from '../../constants';

const KpiIcon: React.FC<{ d: string, className?: string }> = ({ d, className }) => (
  <svg className={`w-8 h-8 ${className}`} fill="none" stroke="currentColor" strokeWidth={1.5} viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
    <path strokeLinecap="round" strokeLinejoin="round" d={d} />
  </svg>
);

interface ForecastCardProps {
    backlogSize: number;
    likelySprints: number;
    conservativeSprints: number;
    optimisticSprints: number;
    avgVelocity: number;
    stdDev: number;
    futureSprints: any[];
}

const formatDateRange = (start: string | undefined, end: string | undefined): string => {
    if (!start || !end) return 'TBD';
    try {
        const startDate = new Date(start);
        const endDate = new Date(end);
        const options: Intl.DateTimeFormatOptions = { month: 'short', day: 'numeric', timeZone: 'UTC' };
        return `${startDate.toLocaleDateString(undefined, options)} - ${endDate.toLocaleDateString(undefined, options)}`;
    } catch(e) {
        return 'Invalid Date';
    }
};

const ForecastCard: React.FC<ForecastCardProps> = ({ 
    backlogSize, 
    likelySprints, 
    conservativeSprints, 
    optimisticSprints,
    avgVelocity,
    stdDev,
    futureSprints,
}) => {
    
    const getHealthStatus = () => {
        if (!isFinite(likelySprints)) return { label: 'Healthy', color: 'text-green-400', description: 'Backlog has substantial work queued.' };
        if (likelySprints < 3) return { label: 'At Risk', color: 'text-red-400', description: 'Backlog may run out of work soon. Prioritize grooming.' };
        if (likelySprints > 8) return { label: 'Needs Refinement', color: 'text-orange-400', description: 'Large backlog. Consider refining and prioritizing to maintain focus.' };
        return { label: 'Healthy', color: 'text-green-400', description: 'Sufficient work for the upcoming sprints.' };
    };
    const healthStatus = getHealthStatus();

    const getConfidence = () => {
        if(avgVelocity <= 0) return { label: 'N/A', color: 'text-slate-400'};
        const cv = stdDev / avgVelocity; // Coefficient of Variation
        if (cv < 0.15) return { label: 'High', color: 'text-green-400' };
        if (cv > 0.35) return { label: 'Low', color: 'text-red-400' };
        return { label: 'Medium', color: 'text-orange-400' };
    };
    const confidence = getConfidence();
    
    const burndownData = [];
    let remainingSp = backlogSize;
    let sprintCounter = 0;
    let depletedSprintName = '';

    while (remainingSp > 0 && sprintCounter < 8) { // Cap at 8 sprints for display
        const sprintInfo = futureSprints[sprintCounter];
        const sprintName = sprintInfo ? sprintInfo.name : `Future Sprint ${sprintCounter + 1}`;
        const sprintDates = sprintInfo ? formatDateRange(sprintInfo.attributes.startDate, sprintInfo.attributes.finishDate) : 'TBD';
        
        const spThisSprint = Math.min(remainingSp, avgVelocity);
        remainingSp = Math.max(0, remainingSp - avgVelocity);

        if (remainingSp === 0 && !depletedSprintName) {
            depletedSprintName = sprintName;
        }

        let rowStatus = { label: 'Healthy', style: 'bg-green-500/10 text-green-400' };
        if (remainingSp < avgVelocity) rowStatus = { label: 'At Risk', style: 'bg-orange-500/10 text-orange-400' };
        if (remainingSp === 0) rowStatus = { label: 'Depleted', style: 'bg-red-500/10 text-red-400' };

        burndownData.push({
            name: sprintName,
            dates: sprintDates,
            burndown: spThisSprint,
            remaining: remainingSp,
            status: rowStatus,
        });

        sprintCounter++;
    }

    return (
      <ChartCard title="Backlog Forecast & Sustainability">
        <div className="flex flex-col h-full p-2 space-y-2 text-sm">
            <div className="grid grid-cols-2 text-center border-b border-slate-700 pb-2">
                <div>
                     <p className={`text-lg font-bold ${healthStatus.color}`}>{healthStatus.label}</p>
                     <p className="text-xs text-slate-400">Backlog Health</p>
                </div>
                <div>
                    <p className={`text-lg font-bold ${confidence.color}`}>{confidence.label}</p>
                    <p className="text-xs text-slate-400">Forecast Confidence</p>
                </div>
            </div>
             <p className="text-xs text-center text-slate-400 px-2">
                With an average velocity of <strong className="text-sky-400">{avgVelocity.toFixed(1)} SP/sprint</strong>, the backlog is likely to be depleted in ~<strong className="text-sky-400">{isFinite(likelySprints) ? likelySprints.toFixed(1) : '∞'} sprints</strong>, around <strong className="text-sky-400">{depletedSprintName || 'a future sprint'}</strong>.
            </p>

            <div className="grid grid-cols-3 text-center text-sm py-2 bg-slate-900/30 rounded-lg divide-x divide-slate-700">
                <div>
                    <p className="text-2xl font-bold text-green-400">{isFinite(conservativeSprints) ? conservativeSprints.toFixed(1) : '∞'}</p>
                    <p className="text-xs text-slate-400">Conservative (sprints)</p>
                </div>
                <div>
                    <p className="text-2xl font-bold text-sky-400">{isFinite(likelySprints) ? likelySprints.toFixed(1) : '∞'}</p>
                    <p className="text-xs text-slate-400">Most Likely (sprints)</p>
                </div>
                <div>
                    <p className="text-2xl font-bold text-orange-400">{isFinite(optimisticSprints) ? optimisticSprints.toFixed(1) : '∞'}</p>
                    <p className="text-xs text-slate-400">Optimistic (sprints)</p>
                </div>
            </div>

            <div className="flex-grow w-full overflow-y-auto pr-2">
                <div role="table" className="text-xs text-slate-300 w-full">
                    <div role="rowheader" className="sticky top-0 bg-slate-800 grid grid-cols-12 gap-2 font-semibold text-slate-400 p-2 border-b border-slate-600">
                        <div role="columnheader" className="col-span-4">Sprint</div>
                        <div role="columnheader" className="col-span-3">Dates</div>
                        <div role="columnheader" className="col-span-2 text-right">Burndown</div>
                        <div role="columnheader" className="col-span-3 text-right">Remaining SP</div>
                    </div>
                    <div role="rowgroup">
                        {burndownData.map((row, index) => (
                           <div key={index} role="row" className="grid grid-cols-12 gap-2 p-2 border-b border-slate-700/50 items-center hover:bg-slate-700/20">
                               <div role="cell" className="col-span-4 font-medium truncate">{row.name}</div>
                               <div role="cell" className="col-span-3 text-slate-400">{row.dates}</div>
                               <div role="cell" className="col-span-2 text-right text-red-400">-{row.burndown.toFixed(1)}</div>
                               <div role="cell" className="col-span-3 text-right font-bold text-lg flex items-center justify-end">
                                    <span className={`mr-2 px-1.5 py-0.5 rounded-full text-[10px] font-semibold ${row.status.style}`}>{row.status.label}</span>
                                   <span>{row.remaining.toFixed(1)}</span>
                               </div>
                           </div>
                        ))}
                    </div>
                </div>
            </div>
        </div>
      </ChartCard>
    );
};

export const BacklogHealthTab: React.FC<TabProps> = ({ data, teams }) => {
  const getBacklogStatus = (count: number): KpiStatus => {
    if (count > 200) return 'warning';
    return 'neutral';
  }

  const kpis = teams.map(team => {
      const backlogData = data.backlogHealth?.[team.name]?.[0];
      if (!backlogData) return [];
      const storyCount = backlogData['Count of User Stories'] as number;
      const storyPoints = backlogData['Sum of Story Points'] as number;
      return [
        { title: `User Stories in Backlog (${team.name})`, value: storyCount, icon: <KpiIcon d={KPI_ICONS.backlog} />, status: getBacklogStatus(storyCount) },
        { title: `Story Points in Backlog (${team.name})`, value: storyPoints, icon: <KpiIcon d={KPI_ICONS.storyPoints} />, status: getBacklogStatus(storyPoints) }
      ];
  }).flat();
  
  const hasDataForAnyTeam = teams.some(team => data.backlogHealth?.[team.name]?.length > 0);

  if (!hasDataForAnyTeam) {
    return <div className="p-6 text-center text-slate-400">No Backlog Health data available for the selected teams.</div>;
  }
  
  const teamMetrics = teams.map(team => {
      const backlogData = data.backlogHealth?.[team.name]?.[0];
      const velocityData = data.sprintVelocityBySprint?.[team.name];
      const futureSprints = data.futureSprints?.[team.name] || [];

      if (!backlogData) return null;

      const healthChart = (
          <ChartCard key={`${team.id}-health`} title={`Backlog Health - ${team.name}`}>
            <ResponsiveContainer width="100%" height="100%">
              <BarChart data={[backlogData]} margin={{ top: 20, right: 30, left: 0, bottom: 5 }}>
                <defs>
                  <linearGradient id="primaryGradient" x1="0" y1="0" x2="0" y2="1">
                    <stop offset="5%" stopColor={CHART_COLORS.primary} stopOpacity={0.9}/>
                    <stop offset="95%" stopColor={CHART_COLORS.accent} stopOpacity={0.6}/>
                  </linearGradient>
                   <linearGradient id="contrastGradient" x1="0" y1="0" x2="0" y2="1">
                    <stop offset="5%" stopColor={CHART_COLORS.contrastPrimary} stopOpacity={0.8}/>
                    <stop offset="95%" stopColor={CHART_COLORS.contrastAccent} stopOpacity={0.5}/>
                  </linearGradient>
                </defs>
                <XAxis dataKey="name" tick={false} />
                <YAxis tick={{ fontSize: 12, fill: CHART_COLORS.text }} stroke={CHART_COLORS.grid} />
                <Tooltip content={<CustomTooltip />} cursor={{fill: 'rgba(14, 165, 233, 0.1)'}} />
                <Legend wrapperStyle={{fontSize: "12px", color: CHART_COLORS.text}}/>
                <Bar dataKey="Count of User Stories" fill="url(#contrastGradient)" animationDuration={800}/>
                <Bar dataKey="Sum of Story Points" fill="url(#primaryGradient)" animationDuration={800}/>
              </BarChart>
            </ResponsiveContainer>
          </ChartCard>
      );

      if (!velocityData || velocityData.length < 3) {
          return [healthChart, (
              <ChartCard key={`${team.id}-forecast`} title={`Backlog Forecast - ${team.name}`}>
                  <div className="flex flex-col items-center justify-center h-full text-slate-400 text-center p-4">
                      <KpiIcon d={KPI_ICONS.velocity} className="text-sky-500 mb-2"/>
                      <h4 className="font-semibold text-slate-200">Forecast Unavailable</h4>
                      <p className="text-xs">Not enough historical velocity data (minimum 3 sprints required) to generate a reliable forecast.</p>
                  </div>
              </ChartCard>
          )];
      }

      const backlogStoryPoints = backlogData['Sum of Story Points'] as number;
      const velocities = velocityData.map(s => s.Completed as number);
      const avgVelocity = velocities.reduce((a, b) => a + b, 0) / velocities.length;
      
      if (backlogStoryPoints <= 0 || avgVelocity <= 0) {
          return [healthChart, (
              <ChartCard key={`${team.id}-forecast`} title={`Backlog Forecast - ${team.name}`}>
                  <div className="flex flex-col items-center justify-center h-full text-slate-400 text-center p-4">
                      <KpiIcon d={KPI_ICONS.backlog} className="text-sky-500 mb-2"/>
                       <h4 className="font-semibold text-slate-200">Forecast Unavailable</h4>
                      <p className="text-xs">{backlogStoryPoints <= 0 ? "No story points in the backlog to forecast." : "Average velocity is zero; cannot calculate forecast."}</p>
                  </div>
              </ChartCard>
          )];
      }

      const sqDiffs = velocities.map(v => Math.pow(v - avgVelocity, 2));
      const avgSqDiff = sqDiffs.reduce((a, b) => a + b, 0) / sqDiffs.length;
      const stdDev = Math.sqrt(avgSqDiff);

      const optimisticVelocity = avgVelocity + 0.75 * stdDev; // Team is faster, backlog consumed sooner
      const conservativeVelocity = Math.max(0.1, avgVelocity - 0.75 * stdDev); // Team is slower, backlog lasts longer

      const likelySprints = backlogStoryPoints / avgVelocity;
      const optimisticSprints = backlogStoryPoints / optimisticVelocity; // Fewer sprints
      const conservativeSprints = backlogStoryPoints / conservativeVelocity; // More sprints

      const forecastCard = (
          <ForecastCard
              key={`${team.id}-forecast`}
              backlogSize={backlogStoryPoints}
              likelySprints={likelySprints}
              conservativeSprints={conservativeSprints}
              optimisticSprints={optimisticSprints}
              avgVelocity={avgVelocity}
              stdDev={stdDev}
              futureSprints={futureSprints}
          />
      );

      return [healthChart, forecastCard];
  }).filter(Boolean).flat();

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
        {kpis.map(kpi => <KpiCard key={kpi.title} title={kpi.title} value={kpi.value} icon={kpi.icon} status={kpi.status} />)}
      </div>
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {teamMetrics}
      </div>
    </div>
  );
};