import React, { useState, useEffect, useMemo } from 'react';
import { AzureDevOpsService } from '../services/api';
import type { Credentials, Project, Team, DashboardConfig } from '../types';

interface SetupProps {
  credentials: Credentials;
  onConfigured: (config: DashboardConfig) => void;
  onLogout: () => void;
}

const Loader: React.FC<{ text: string }> = ({ text }) => (
    <div className="flex items-center space-x-2 text-slate-400">
        <svg className="animate-spin h-5 w-5 text-sky-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
        <span>{text}</span>
    </div>
);

export const Setup: React.FC<SetupProps> = ({ credentials, onConfigured, onLogout }) => {
  const [projects, setProjects] = useState<Project[]>([]);
  const [selectedProject, setSelectedProject] = useState<Project | null>(null);
  const [teams, setTeams] = useState<Team[]>([]);
  const [selectedTeams, setSelectedTeams] = useState<Map<string, Team>>(new Map());
  const [loading, setLoading] = useState<'projects' | 'teams' | 'none'>('projects');
  const [error, setError] = useState<string | null>(null);

  const apiService = useMemo(() => new AzureDevOpsService(credentials.organization, credentials.pat), [credentials]);

  useEffect(() => {
    const fetchProjects = async () => {
      setLoading('projects');
      setError(null);
      try {
        const projectList = await apiService.getProjects();
        setProjects(projectList);
      } catch (e) {
        setError(e instanceof Error ? e.message : 'Failed to fetch projects. The PAT might lack permissions.');
      } finally {
        setLoading('none');
      }
    };
    fetchProjects();
  }, [apiService]);

  useEffect(() => {
    if (selectedProject) {
      const fetchTeams = async () => {
        setLoading('teams');
        setError(null);
        setTeams([]);
        setSelectedTeams(new Map());
        try {
          const teamList = await apiService.getTeams(selectedProject.id);
          setTeams(teamList);
        } catch (e) {
          setError(e instanceof Error ? e.message : 'Failed to fetch teams for the selected project.');
        } finally {
          setLoading('none');
        }
      };
      fetchTeams();
    }
  }, [selectedProject, apiService]);

  const handleTeamToggle = (team: Team) => {
    setSelectedTeams(prev => {
      const newMap = new Map(prev);
      if (newMap.has(team.id)) {
        newMap.delete(team.id);
      } else {
        newMap.set(team.id, team);
      }
      return newMap;
    });
  };

  const handleProjectChange = (projectId: string) => {
    const project = projects.find(p => p.id === projectId) || null;
    setSelectedProject(project);
  }

  const handleViewDashboard = () => {
    if (selectedProject && selectedTeams.size > 0) {
      onConfigured({ project: selectedProject, teams: Array.from(selectedTeams.values()) });
    }
  };

  return (
    <div className="flex items-center justify-center min-h-screen">
      <div className="w-full max-w-2xl p-8 bg-slate-800 rounded-2xl shadow-2xl shadow-slate-900/50 relative">
        <button onClick={onLogout} className="absolute top-6 right-6 text-sm font-medium text-sky-500 hover:text-sky-400 transition">
          Logout
        </button>
        <div className="space-y-6">
          <div>
            <h2 className="text-3xl font-extrabold text-center text-slate-100">Dashboard Setup</h2>
            <p className="mt-2 text-sm text-center text-slate-400">Select a project and teams to analyze.</p>
          </div>

          {error && (
              <div className="p-4 text-sm text-red-300 bg-red-900/50 border border-red-500/50 rounded-md">
                  <p className="font-bold">Error</p>
                  <p>{error}</p>
              </div>
          )}

          <div className="space-y-6">
            <div>
              <label htmlFor="project" className="block text-lg font-medium text-slate-300">1. Select a Project</label>
              {loading === 'projects' ? <Loader text="Loading projects..." /> : (
                <select
                  id="project"
                  name="project"
                  onChange={(e) => handleProjectChange(e.target.value)}
                  value={selectedProject?.id || ''}
                  className="mt-2 block w-full px-3 py-2 bg-slate-700 border border-slate-600 rounded-md shadow-sm text-slate-200 focus:outline-none focus:ring-sky-500 focus:border-sky-500 sm:text-sm"
                  disabled={projects.length === 0}
                >
                  <option value="" disabled>-- Please choose a project --</option>
                  {projects.map(p => <option key={p.id} value={p.id}>{p.name}</option>)}
                </select>
              )}
            </div>

            {selectedProject && (
              <div className="border-t border-slate-700 pt-6">
                <label className="block text-lg font-medium text-slate-300">2. Select Teams</label>
                {loading === 'teams' ? <Loader text="Loading teams..." /> : (
                  <div className="mt-2 max-h-60 overflow-y-auto p-3 bg-slate-900/50 border border-slate-700 rounded-md space-y-3">
                    {teams.length > 0 ? teams.map(team => (
                      <div key={team.id} className="flex items-center">
                        <input
                          id={`team-${team.id}`}
                          type="checkbox"
                          checked={selectedTeams.has(team.id)}
                          onChange={() => handleTeamToggle(team)}
                          className="h-4 w-4 text-sky-600 bg-slate-700 border-slate-600 rounded focus:ring-sky-500"
                        />
                        <label htmlFor={`team-${team.id}`} className="ml-3 block text-sm font-medium text-slate-300">{team.name}</label>
                      </div>
                    )) : <p className="text-sm text-slate-500">No teams found for this project.</p>}
                  </div>
                )}
              </div>
            )}
          </div>
          
          <div className="pt-6">
            <button
              onClick={handleViewDashboard}
              disabled={!selectedProject || selectedTeams.size === 0 || loading !== 'none'}
              className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-sky-600 hover:bg-sky-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-slate-800 focus:ring-sky-500 disabled:bg-slate-600 disabled:cursor-not-allowed transition"
            >
              View Dashboard
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};