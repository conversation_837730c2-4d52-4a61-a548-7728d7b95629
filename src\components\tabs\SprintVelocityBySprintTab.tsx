import React from 'react';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>hart, Bar, XAxis, YAxis, <PERSON>lt<PERSON>, Legend, LabelList } from 'recharts';
import { ChartCard } from '../ChartCard';
import { KpiCard } from '../common/KpiCard';
import { CustomTooltip } from './CustomTooltip';
import type { TabProps, ChartData, KpiStatus } from '../../types';
import { CHART_COLORS, KPI_ICONS } from '../../constants';

export const SprintVelocityBySprintTab: React.FC<TabProps> = ({ data, teams }) => {
  const teamHasData = (teamName: string) => data.sprintVelocityBySprint && data.sprintVelocityBySprint[teamName] && data.sprintVelocityBySprint[teamName].length > 0;
  const noDataForTab = !teams.some(team => teamHasData(team.name));

  const KpiIcon: React.FC<{ d: string, className?: string }> = ({ d, className }) => (
    <svg className={`w-8 h-8 ${className}`} fill="none" stroke="currentColor" strokeWidth={1.5} viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
      <path strokeLinecap="round" strokeLinejoin="round" d={d} />
    </svg>
  );

  if (noDataForTab) {
    return <div className="p-6 text-center text-slate-400">No Sprint Velocity data available for the selected teams.</div>;
  }

  const getReliabilityStatus = (rate: number): KpiStatus => {
    if (rate >= 90) return 'good';
    if (rate >= 80) return 'neutral';
    if (rate >= 70) return 'warning';
    return 'danger';
  };

  return (
    <div className="space-y-6">
       <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
        {teams.filter(team => teamHasData(team.name)).map(team => {
            const teamData = data.sprintVelocityBySprint[team.name];
            const totalCompleted = teamData.reduce((acc: number, curr: ChartData) => acc + (curr.Completed as number), 0);
            const totalCommitted = teamData.reduce((acc: number, curr: ChartData) => acc + (curr.Committed as number), 0);
            const totalRemoved = teamData.reduce((acc: number, curr: ChartData) => acc + ((curr.Removed as number) || 0), 0);
            const avgVelocity = totalCompleted / teamData.length;
            const committedForReliability = totalCommitted - totalRemoved;
            const reliability = committedForReliability > 0 ? (totalCompleted / committedForReliability) * 100 : 100;

            return [
                <KpiCard 
                    key={`${team.id}-velocity`} 
                    title={`Avg. Sprint Velocity (${team.name})`} 
                    value={avgVelocity.toFixed(1)} 
                    icon={<KpiIcon d={KPI_ICONS.velocity} />}
                    status="neutral"
                />,
                <KpiCard 
                    key={`${team.id}-reliability`} 
                    title={`Commitment Reliability (${team.name})`} 
                    value={`${reliability.toFixed(1)}%`} 
                    icon={<KpiIcon d={KPI_ICONS.reliability} />} 
                    status={getReliabilityStatus(reliability)}
                />
            ];
        }).flat()}
      </div>
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {teams.map(team => teamHasData(team.name) && (
          <ChartCard key={team.id} title={`Sprint Velocity By Sprint - ${team.name}`}>
            <ResponsiveContainer width="100%" height="100%">
              <BarChart data={data.sprintVelocityBySprint[team.name]} margin={{ top: 20, right: 30, left: 0, bottom: 20 }}>
                 <defs>
                  <linearGradient id="velSprintPrimaryGradient" x1="0" y1="0" x2="0" y2="1">
                    <stop offset="5%" stopColor={CHART_COLORS.primary} stopOpacity={0.9}/>
                    <stop offset="95%" stopColor={CHART_COLORS.accent} stopOpacity={0.6}/>
                  </linearGradient>
                  <linearGradient id="velSprintContrastGradient" x1="0" y1="0" x2="0" y2="1">
                    <stop offset="5%" stopColor={CHART_COLORS.contrastPrimary} stopOpacity={0.7}/>
                    <stop offset="95%" stopColor={CHART_COLORS.contrastAccent} stopOpacity={0.4}/>
                  </linearGradient>
                  <linearGradient id="velSprintDangerGradient" x1="0" y1="0" x2="0" y2="1">
                    <stop offset="5%" stopColor={CHART_COLORS.danger} stopOpacity={0.8}/>
                    <stop offset="95%" stopColor={CHART_COLORS.dangerAccent} stopOpacity={0.5}/>
                  </linearGradient>
                </defs>
                <XAxis dataKey="name" tick={{ fontSize: 10, fill: CHART_COLORS.text }} angle={-25} textAnchor="end" height={50} interval={0} stroke={CHART_COLORS.grid} />
                <YAxis tick={{ fontSize: 12, fill: CHART_COLORS.text }} stroke={CHART_COLORS.grid} />
                <Tooltip content={<CustomTooltip />} cursor={{fill: 'rgba(14, 165, 233, 0.1)'}}/>
                <Legend wrapperStyle={{fontSize: "12px", color: CHART_COLORS.text}}/>
                <Bar dataKey="Completed" fill="url(#velSprintPrimaryGradient)" animationDuration={800}>
                    <LabelList dataKey="Completed" position="top" fontSize={10} fill={CHART_COLORS.text} />
                </Bar>
                <Bar dataKey="Committed" fill="url(#velSprintContrastGradient)" animationDuration={800} />
                <Bar dataKey="Removed" fill="url(#velSprintDangerGradient)" animationDuration={800} />
              </BarChart>
            </ResponsiveContainer>
          </ChartCard>
        ))}
      </div>
    </div>
  );
};