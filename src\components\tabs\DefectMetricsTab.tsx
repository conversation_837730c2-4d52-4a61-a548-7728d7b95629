import React from 'react';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>hart, Bar, XAxis, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Legend, LabelList, Composed<PERSON>hart } from 'recharts';
import { ChartCard } from '../ChartCard';
import { KpiCard } from '../common/KpiCard';
import { CustomTooltip } from './CustomTooltip';
import type { TabProps, ChartData, KpiStatus } from '../../types';
import { CHART_COLORS, KPI_ICONS } from '../../constants';

const KpiIcon: React.FC<{ d: string, className?: string }> = ({ d, className }) => (
    <svg className={`w-8 h-8 ${className}`} fill="none" stroke="currentColor" strokeWidth={1.5} viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
      <path strokeLinecap="round" strokeLinejoin="round" d={d} />
    </svg>
);

export const DefectMetricsTab: React.FC<TabProps> = ({ data, teams }) => {
  if (!data.defectMetrics || Object.keys(data.defectMetrics).length === 0) {
    return <div className="p-6 text-center text-slate-400">No Defect Metrics data available for the selected teams.</div>;
  }
  
  const getDefectStatus = (count: number): KpiStatus => {
    if (count > 10) return 'danger';
    if (count > 5) return 'warning';
    return 'good';
  };

  const getDensityStatus = (density: number): KpiStatus => {
    if (density > 0.5) return 'danger';
    if (density > 0.25) return 'warning';
    return 'good';
  };

  const getProdDefectStatus = (count: number): KpiStatus => {
    if (count > 0) return 'danger';
    return 'good';
  };

  return (
    <div className="space-y-8">
      {teams.map(team => {
        const teamData = data.defectMetrics[team.name];
        if (!teamData) return null;

        const totalDefects = teamData.rejection.reduce((acc: number, curr: ChartData) => acc + (curr['Testing Defects'] as number), 0);
        const avgDensity = teamData.density.length > 0 ? (teamData.density.reduce((acc: number, curr: ChartData) => acc + (curr['Density'] as number), 0) / teamData.density.length) : 0;
        const prodDefects = teamData.leakage.reduce((acc: number, curr: ChartData) => acc + (curr['Production Defects Count'] as number), 0);
        
        return (
          <div key={team.id}>
            <h2 className="text-3xl font-bold text-slate-100 mb-4 border-b-2 border-slate-700 pb-2 tracking-tight">{team.name}: Defect Metrics</h2>
            
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4 mb-6">
                <KpiCard title="Total Defects (Sprints)" value={totalDefects} icon={<KpiIcon d={KPI_ICONS.defects} />} status={getDefectStatus(totalDefects)} />
                <KpiCard title="Avg Defect Density" value={avgDensity.toFixed(2)} icon={<KpiIcon d={KPI_ICONS.density} />} status={getDensityStatus(avgDensity)} />
                <KpiCard title="Production Defects" value={prodDefects} icon={<KpiIcon d={KPI_ICONS.leakage} />} status={getProdDefectStatus(prodDefects)} />
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
              <ChartCard title="Defect Rejection Ratio">
                <ResponsiveContainer width="100%" height="100%">
                  <ComposedChart data={teamData.rejection} margin={{ top: 20, right: 30, left: 0, bottom: 20 }}>
                     <defs>
                      <linearGradient id="primaryGradient" x1="0" y1="0" x2="0" y2="1">
                        <stop offset="5%" stopColor={CHART_COLORS.primary} stopOpacity={0.9}/>
                        <stop offset="95%" stopColor={CHART_COLORS.accent} stopOpacity={0.6}/>
                      </linearGradient>
                    </defs>
                    <XAxis dataKey="name" tick={{ fontSize: 12, fill: CHART_COLORS.text }} stroke={CHART_COLORS.grid} label={{ value: 'Sprints', position: 'insideBottom', offset: -10, fontSize: 12, fill: CHART_COLORS.text }} />
                    <YAxis yAxisId="left" tick={{ fontSize: 12, fill: CHART_COLORS.text }} stroke={CHART_COLORS.grid} />
                    <Tooltip content={<CustomTooltip />} cursor={{fill: 'rgba(14, 165, 233, 0.1)'}} />
                    <Legend wrapperStyle={{fontSize: "12px", color: CHART_COLORS.text}}/>
                    <Bar yAxisId="left" dataKey="Testing Defects" fill="url(#primaryGradient)" animationDuration={800} animationEasing="ease-out">
                      <LabelList dataKey="Testing Defects" position="top" fontSize={10} fill={CHART_COLORS.text} />
                    </Bar>
                  </ComposedChart>
                </ResponsiveContainer>
              </ChartCard>
              <ChartCard title="Defect Density By Story Points">
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart data={teamData.density} margin={{ top: 20, right: 30, left: 0, bottom: 5 }}>
                    <defs>
                      <linearGradient id="warningGradient" x1="0" y1="0" x2="0" y2="1">
                        <stop offset="5%" stopColor={CHART_COLORS.warning} stopOpacity={0.9}/>
                        <stop offset="95%" stopColor={CHART_COLORS.warningAccent} stopOpacity={0.6}/>
                      </linearGradient>
                    </defs>
                    <XAxis dataKey="name" tick={{ fontSize: 12, fill: CHART_COLORS.text }} stroke={CHART_COLORS.grid} />
                    <YAxis tick={{ fontSize: 12, fill: CHART_COLORS.text }} stroke={CHART_COLORS.grid} domain={[0, 'auto']}/>
                    <Tooltip content={<CustomTooltip />} cursor={{fill: 'rgba(249, 115, 22, 0.1)'}}/>
                    <Bar dataKey="Density" fill="url(#warningGradient)" animationDuration={800} animationEasing="ease-out">
                      <LabelList dataKey="Density" position="top" formatter={(v:number) => v.toFixed(2)} fontSize={10} fill={CHART_COLORS.text} />
                    </Bar>
                  </BarChart>
                </ResponsiveContainer>
              </ChartCard>
              <ChartCard title="Production Defect Leakage">
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart data={teamData.leakage} margin={{ top: 20, right: 30, left: 0, bottom: 20 }}>
                     <defs>
                      <linearGradient id="leakagePrimaryGradient" x1="0" y1="0" x2="0" y2="1">
                        <stop offset="5%" stopColor={CHART_COLORS.primary} stopOpacity={0.9}/>
                        <stop offset="95%" stopColor={CHART_COLORS.accent} stopOpacity={0.6}/>
                      </linearGradient>
                      <linearGradient id="dangerGradient" x1="0" y1="0" x2="0" y2="1">
                        <stop offset="5%" stopColor={CHART_COLORS.danger} stopOpacity={0.9}/>
                        <stop offset="95%" stopColor={CHART_COLORS.dangerAccent} stopOpacity={0.6}/>
                      </linearGradient>
                    </defs>
                    <XAxis dataKey="name" tick={{ fontSize: 12, fill: CHART_COLORS.text }} stroke={CHART_COLORS.grid} label={{ value: 'Sprints', position: 'insideBottom', offset: -10, fontSize: 12, fill: CHART_COLORS.text }} />
                    <YAxis tick={{ fontSize: 12, fill: CHART_COLORS.text }} stroke={CHART_COLORS.grid} allowDecimals={false} />
                    <Tooltip content={<CustomTooltip />} cursor={{fill: 'rgba(14, 165, 233, 0.1)'}} />
                    <Legend wrapperStyle={{fontSize: "12px", color: CHART_COLORS.text}}/>
                    <Bar dataKey="Testing Defects Count" stackId="a" fill="url(#leakagePrimaryGradient)" animationDuration={800} animationEasing="ease-out" />
                    <Bar dataKey="Production Defects Count" stackId="a" fill="url(#dangerGradient)" animationDuration={800} animationEasing="ease-out">
                       <LabelList dataKey="Production Defects Count" position="top" formatter={(v: number) => v > 0 ? v : ''} fontSize={10} fill={CHART_COLORS.text} />
                    </Bar>
                  </BarChart>
                </ResponsiveContainer>
              </ChartCard>
            </div>
          </div>
        )
      })}
    </div>
  );
};