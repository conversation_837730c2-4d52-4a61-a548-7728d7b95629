import React from 'react';

interface ChartCardProps {
  title: string;
  children: React.ReactNode;
}

export const ChartCard: React.FC<ChartCardProps> = ({ title, children }) => {
  return (
    <div className="bg-slate-800/50 backdrop-blur-sm p-4 pt-3 shadow-lg rounded-lg border border-slate-700 h-[400px] flex flex-col border-t-4 border-t-sky-500 transition-all duration-300 hover:shadow-2xl hover:shadow-sky-500/20 hover:border-sky-400/80">
      <h3 className="text-xl font-semibold text-slate-100 mb-4 text-center tracking-tight">{title}</h3>
      <div className="flex-grow w-full h-full">
        {children}
      </div>
    </div>
  );
};