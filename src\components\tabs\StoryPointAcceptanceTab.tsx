import React from 'react';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>r, BarChart, Bar, XAxis, YAxis, Tooltip, ReferenceLine, LabelList } from 'recharts';
import { ChartCard } from '../ChartCard';
import { KpiCard } from '../common/KpiCard';
import { CustomTooltip } from './CustomTooltip';
import type { TabProps, ChartData, KpiStatus } from '../../types';
import { CHART_COLORS, KPI_ICONS } from '../../constants';

export const StoryPointAcceptanceTab: React.FC<TabProps> = ({ data, teams }) => {
  const teamHasData = (teamName: string) => data.storyPointAcceptance && data.storyPointAcceptance[teamName] && data.storyPointAcceptance[teamName].length > 0;
  const noDataForTab = !teams.some(team => teamHasData(team.name));

  const KpiIcon: React.FC<{ d: string, className?: string }> = ({ d, className }) => (
    <svg className={`w-8 h-8 ${className}`} fill="none" stroke="currentColor" strokeWidth={1.5} viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
      <path strokeLinecap="round" strokeLinejoin="round" d={d} />
    </svg>
  );

  if (noDataForTab) {
    return <div className="p-6 text-center text-slate-400">No Story Point Acceptance data available for the selected teams.</div>;
  }

  const getStatus = (rate: number): KpiStatus => {
    if (rate >= 95) return 'good';
    if (rate >= 90) return 'neutral';
    if (rate >= 80) return 'warning';
    return 'danger';
  };
  
  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
        {teams.filter(team => teamHasData(team.name)).map(team => {
            const teamData = data.storyPointAcceptance[team.name];
            const avgAcceptance = teamData.reduce((acc: number, curr: ChartData) => acc + (curr.Acceptance as number), 0) / teamData.length;
            return (
              <KpiCard 
                key={team.id} 
                title={`Avg. Acceptance (${team.name})`} 
                value={`${avgAcceptance.toFixed(1)}%`} 
                icon={<KpiIcon d={KPI_ICONS.acceptance} />}
                status={getStatus(avgAcceptance)} 
              />
            )
        })}
      </div>
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {teams.map(team => teamHasData(team.name) && (
          <ChartCard key={team.id} title={`% Story Point Acceptance - ${team.name}`}>
            <ResponsiveContainer width="100%" height="100%">
              <BarChart data={data.storyPointAcceptance[team.name]} margin={{ top: 20, right: 30, left: 10, bottom: 5 }}>
                <defs>
                  <linearGradient id="acceptanceGradient" x1="0" y1="0" x2="0" y2="1">
                    <stop offset="5%" stopColor={CHART_COLORS.good} stopOpacity={0.9}/>
                    <stop offset="95%" stopColor={CHART_COLORS.goodAccent} stopOpacity={0.6}/>
                  </linearGradient>
                </defs>
                <XAxis dataKey="name" tick={{ fontSize: 12, fill: CHART_COLORS.text }} stroke={CHART_COLORS.grid} />
                <YAxis domain={[0, 100]} label={{ value: '% Acceptance', angle: -90, position: 'insideLeft', style: {textAnchor: 'middle', fontSize: 12, fill: CHART_COLORS.text} }} tick={{ fontSize: 12, fill: CHART_COLORS.text }} stroke={CHART_COLORS.grid} />
                <Tooltip content={<CustomTooltip />} cursor={{fill: 'rgba(34, 197, 94, 0.1)'}} />
                <ReferenceLine y={95} label={{ value: 'Target: 95%', position: 'insideTopRight', fill: '#0ea5e9', fontSize: 12, fontWeight: 'bold' }} stroke="#0ea5e9" strokeDasharray="3 3" />
                <Bar dataKey="Acceptance" fill="url(#acceptanceGradient)" animationDuration={800}>
                  <LabelList dataKey="Acceptance" position="top" formatter={(value: number) => value.toFixed(0) + '%'} fontSize={10} fill={CHART_COLORS.text} />
                </Bar>
              </BarChart>
            </ResponsiveContainer>
          </ChartCard>
        ))}
      </div>
    </div>
  );
};