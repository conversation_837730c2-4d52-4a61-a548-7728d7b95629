import React from 'react';

export const CustomTooltip = ({ active, payload }: any) => {
  if (active && payload && payload.length) {
    const data = payload[0].payload;
    const mainLabel = data.Month || data.name;

    const excludedKeysFromMoreInfo = ['name', 'Month', 'payload', ...payload.map((p: any) => p.dataKey)];

    const formatValue = (pld: any) => {
      const { value, dataKey } = pld;
      if (typeof value !== 'number') return value;
      
      const keyLower = String(dataKey).toLowerCase();
      if (keyLower.includes('acceptance') || keyLower.includes('ratio') || keyLower.includes('effectiveness') || keyLower.includes('reliability')) {
          return `${value.toFixed(1)}%`;
      }
      if (keyLower.includes('density')) {
          return value.toFixed(2);
      }
      return value.toFixed(0);
    };
    
    const moreInfoData = Object.entries(data).filter(([key, value]) => !excludedKeysFromMoreInfo.includes(key) && String(value).length < 25 && value !== 0);

    return (
      <div className="p-3 bg-slate-800/90 backdrop-blur-sm border border-slate-600 rounded-lg shadow-xl text-sm min-w-[200px]">
        <p className="font-bold text-slate-100 mb-2">{mainLabel}</p>
        <div className="space-y-1">
          {payload.map((pld: any) => (
            <div key={pld.dataKey} className="flex justify-between items-center">
              <div className="flex items-center">
                <span style={{ backgroundColor: pld.fill || pld.stroke }} className="w-2.5 h-2.5 rounded-full mr-2 flex-shrink-0"></span>
                <span className="text-slate-300 mr-2">{pld.name}:</span>
              </div>
              <span className="font-bold text-slate-100">
                {formatValue(pld)}
              </span>
            </div>
          ))}
        </div>
        
        {moreInfoData.length > 0 && 
            <>
                <div className="border-t border-slate-700 my-2"></div>
                <div className="space-y-1">
                {moreInfoData.map(([key, value]) => (
                    <div key={key} className="flex justify-between text-xs text-slate-400">
                        <span>{key}</span>
                        <span className="font-medium text-slate-300">{String(value)}</span>
                    </div>
                ))}
                </div>
            </>
        }
      </div>
    );
  }
  return null;
};