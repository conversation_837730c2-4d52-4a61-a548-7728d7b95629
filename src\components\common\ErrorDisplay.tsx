import React from 'react';

interface ErrorDisplayProps {
  error: string;
}

export const ErrorDisplay: React.FC<ErrorDisplayProps> = ({ error }) => (
    <div className="flex items-center justify-center h-full p-4">
        <div className="w-full max-w-lg p-8 space-y-4 bg-slate-800 rounded-lg shadow-lg border border-red-500/30 text-center">
             <svg className="w-16 h-16 mx-auto text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg>
            <h3 className="text-xl font-bold text-red-400">Error Fetching Data</h3>
            <p className="text-sm text-slate-300">{error}</p>
            <p className="text-xs text-slate-400 mt-4">
                Please verify your Personal Access Token has 'Read' access to Work Items, Project and Team, and Test Management. Also check the Organization name and your network connection.
            </p>
        </div>
    </div>
);