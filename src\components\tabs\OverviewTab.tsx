import React from 'react';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Bar, XAxis, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Legend } from 'recharts';
import { KpiCard } from '../common/KpiCard';
import { ChartCard } from '../ChartCard';
import { CustomTooltip } from './CustomTooltip';
import type { TabProps, ChartData, Team, KpiStatus } from '../../types';
import { CHART_COLORS, KPI_ICONS } from '../../constants';

const KpiIcon: React.FC<{ d: string, className?: string }> = ({ d, className }) => (
    <svg className={`w-8 h-8 ${className}`} fill="none" stroke="currentColor" strokeWidth={1.5} viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
      <path strokeLinecap="round" strokeLinejoin="round" d={d} />
    </svg>
);

const calculateOverallAverage = (data: Record<string, Chart<PERSON><PERSON>[]> | undefined, teams: Team[], key: string): number => {
    if (!data) return 0;
    const allValues = teams.flatMap(team => (data[team.name] || []).map(item => item[key] as number));
    if (allValues.length === 0) return 0;
    return allValues.reduce((sum, val) => sum + val, 0) / allValues.length;
};

const calculateOverallSum = (data: Record<string, any> | undefined, teams: Team[], subKey: string | null, dataKey: string): number => {
    if (!data) return 0;
    return teams.reduce((total, team) => {
        const teamMetricData = data[team.name];
        if (!teamMetricData) return total;

        const dataArray = subKey ? (teamMetricData[subKey] || []) : (teamMetricData || []);

        if (!Array.isArray(dataArray)) {
            return total;
        }

        const teamSum = dataArray.reduce((acc: number, curr: ChartData) => acc + ((curr[dataKey] as number) || 0), 0);
        return total + teamSum;
    }, 0);
};

const getTeamVelocityComparisonData = (data: Record<string, ChartData[]> | undefined, teams: Team[]): ChartData[] => {
    if (!data) return [];
    return teams.map(team => {
        const teamData = data[team.name] || [];
        if (teamData.length === 0) return { name: team.name, 'Avg Velocity': 0 };
        const avgVelocity = teamData.reduce((acc, curr) => acc + (curr.Completed as number), 0) / teamData.length;
        return { name: team.name, 'Avg Velocity': avgVelocity };
    });
}

interface KpiData {
    title: string;
    value: string | number;
    icon: React.ReactNode;
    status: KpiStatus;
}

export const OverviewTab: React.FC<TabProps> = ({ data, teams, project }) => {
    const overallAcceptance = calculateOverallAverage(data.storyPointAcceptance, teams, 'Acceptance');
    const overallVelocity = calculateOverallAverage(data.sprintVelocityBySprint, teams, 'Completed');
    const totalDefects = calculateOverallSum(data.defectMetrics, teams, 'rejection', 'Testing Defects');
    const totalBacklogStories = calculateOverallSum(data.backlogHealth, teams, null, 'Count of User Stories');
    
    const teamVelocityData = getTeamVelocityComparisonData(data.sprintVelocityBySprint, teams);

    const getAcceptanceStatus = (rate: number): KpiStatus => {
        if (rate >= 95) return 'good';
        if (rate >= 90) return 'neutral';
        if (rate >= 80) return 'warning';
        return 'danger';
    };

    const getDefectStatus = (count: number): KpiStatus => {
        if (count > 50) return 'danger';
        if (count > 25) return 'warning';
        return 'neutral';
    };
    
    const getBacklogStatus = (count: number): KpiStatus => {
        if (count > 200) return 'warning';
        return 'neutral';
    };

    const kpis: KpiData[] = [
        { title: 'Project-wide Avg. Acceptance', value: `${overallAcceptance.toFixed(1)}%`, icon: <KpiIcon d={KPI_ICONS.acceptance} />, status: getAcceptanceStatus(overallAcceptance) },
        { title: 'Project-wide Avg. Velocity', value: overallVelocity.toFixed(1), icon: <KpiIcon d={KPI_ICONS.velocity} />, status: 'neutral' },
        { title: 'Total Active Defects', value: totalDefects, icon: <KpiIcon d={KPI_ICONS.defects} />, status: getDefectStatus(totalDefects) },
        { title: 'Total Stories in Backlogs', value: totalBacklogStories, icon: <KpiIcon d={KPI_ICONS.backlog} />, status: getBacklogStatus(totalBacklogStories) },
    ];
    
    return (
        <div className="space-y-6">
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                {kpis.map(kpi => <KpiCard key={kpi.title} title={kpi.title} value={kpi.value} icon={kpi.icon} status={kpi.status} />)}
            </div>
            
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                 <ChartCard title="Team Velocity Comparison (Avg per Sprint)">
                    <ResponsiveContainer width="100%" height="100%">
                        <BarChart data={teamVelocityData} layout="vertical" margin={{ top: 5, right: 30, left: 30, bottom: 5 }}>
                             <defs>
                                <linearGradient id="teamVelocityGradient" x1="0" y1="0" x2="1" y2="0">
                                    <stop offset="5%" stopColor={CHART_COLORS.accent} stopOpacity={0.9}/>
                                    <stop offset="95%" stopColor={CHART_COLORS.primary} stopOpacity={0.7}/>
                                </linearGradient>
                            </defs>
                            <XAxis type="number" tick={{ fontSize: 12, fill: CHART_COLORS.text }} stroke={CHART_COLORS.grid} />
                            <YAxis type="category" dataKey="name" width={100} tick={{ fontSize: 12, fill: CHART_COLORS.text }} stroke={CHART_COLORS.grid} />
                            <Tooltip content={<CustomTooltip />} cursor={{fill: 'rgba(14, 165, 233, 0.1)'}} />
                            <Bar dataKey="Avg Velocity" fill="url(#teamVelocityGradient)" barSize={20} animationDuration={800} />
                        </BarChart>
                    </ResponsiveContainer>
                </ChartCard>

                <ChartCard title={`Project Backlog Health - ${project.name}`}>
                     <ResponsiveContainer width="100%" height="100%">
                        <BarChart 
                            data={teams.map(t => data.backlogHealth?.[t.name]?.[0] || { name: t.name }).map(d => ({...d, name: d.name.split('\\').pop() }))} 
                            margin={{ top: 20, right: 30, left: 0, bottom: 5 }}
                        >
                            <defs>
                              <linearGradient id="backlogPrimaryGradient" x1="0" y1="0" x2="0" y2="1">
                                <stop offset="5%" stopColor={CHART_COLORS.primary} stopOpacity={0.9}/>
                                <stop offset="95%" stopColor={CHART_COLORS.accent} stopOpacity={0.6}/>
                              </linearGradient>
                               <linearGradient id="backlogContrastGradient" x1="0" y1="0" x2="0" y2="1">
                                <stop offset="5%" stopColor={CHART_COLORS.contrastPrimary} stopOpacity={0.8}/>
                                <stop offset="95%" stopColor={CHART_COLORS.contrastAccent} stopOpacity={0.5}/>
                              </linearGradient>
                            </defs>
                            <XAxis dataKey="name" tick={{ fontSize: 12, fill: CHART_COLORS.text }} stroke={CHART_COLORS.grid} />
                            <YAxis tick={{ fontSize: 12, fill: CHART_COLORS.text }} stroke={CHART_COLORS.grid} />
                            <Tooltip content={<CustomTooltip />} cursor={{fill: 'rgba(14, 165, 233, 0.1)'}} />
                            <Legend wrapperStyle={{fontSize: "12px", color: CHART_COLORS.text}}/>
                            <Bar dataKey="Count of User Stories" fill="url(#backlogContrastGradient)" animationDuration={800} />
                            <Bar dataKey="Sum of Story Points" fill="url(#backlogPrimaryGradient)" animationDuration={800} />
                        </BarChart>
                    </ResponsiveContainer>
                </ChartCard>
            </div>
        </div>
    );
};