import { MetricTab } from './types';

const BUG_ICON = "M15.362 5.214A8.252 8.252 0 0 1 12 21 8.25 8.25 0 0 1 6.038 7.048 8.287 8.287 0 0 0 9 9.601a8.983 8.983 0 0 1 3.361-6.867 8.21 8.21 0 0 0 3 2.48Z M12 18a3.75 3.75 0 0 0 .495-7.467 3.75 3.75 0 0 0-7.993-2.19c-.313.652-.615 1.355-.826 2.095a3.75 3.75 0 0 0 5.228 5.472.75.75 0 0 0 .004-1.065a.75.75 0 0 0-1.065-.004";

export const ALL_METRIC_TABS: MetricTab[] = [
  MetricTab.Overview,
  MetricTab.StoryPointAcceptance,
  MetricTab.SprintVelocityByMonth,
  MetricTab.SprintVelocityBySprint,
  MetricTab.DefectMetrics,
  MetricTab.TestMetrics,
  MetricTab.BacklogHealth,
];

export const TAB_ICONS: Record<MetricTab, string> = {
  [MetricTab.Overview]: "M3.75 3v11.25A2.25 2.25 0 006 16.5h12A2.25 2.25 0 0020.25 14.25V3M3.75 3h16.5M3.75 3A2.25 2.25 0 001.5 5.25v13.5A2.25 2.25 0 003.75 21h16.5A2.25 2.25 0 0022.5 18.75V5.25A2.25 2.25 0 0020.25 3",
  [MetricTab.StoryPointAcceptance]: "M9 12.75L11.25 15 15 9.75M21 12a9 9 0 11-18 0 9 9 0 0118 0z",
  [MetricTab.SprintVelocityByMonth]: "M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 012.25-2.25h13.5A2.25 2.25 0 0121 7.5v11.25m-18 0A2.25 2.25 0 005.25 21h13.5A2.25 2.25 0 0021 18.75m-18 0h18M12 12.75h.008v.008H12v-.008z",
  [MetricTab.SprintVelocityBySprint]: "M3.75 13.5l10.5-11.25L12 10.5h8.25L9.75 21.75 12 13.5H3.75z",
  [MetricTab.DefectMetrics]: BUG_ICON,
  [MetricTab.TestMetrics]: "M16.5 10.5V6.75a4.5 4.5 0 10-9 0v3.75m-.75 11.25h10.5a2.25 2.25 0 002.25-2.25v-6.75a2.25 2.25 0 00-2.25-2.25H6.75a2.25 2.25 0 00-2.25 2.25v6.75a2.25 2.25 0 002.25 2.25z",
  [MetricTab.BacklogHealth]: "M3.75 6.75h16.5M3.75 12h16.5m-16.5 5.25h16.5",
};


export const CHART_COLORS = {
  primary: '#60a5fa', // blue-400
  secondary: '#93c5fd', // blue-300
  accent: '#2563eb', // blue-600
  contrastPrimary: '#5eead4', // teal-300
  contrastAccent: '#14b8a6', // teal-500
  danger: '#f87171', // red-400
  dangerAccent: '#dc2626', // red-600
  warning: '#fb923c', // orange-400
  warningAccent: '#f97316', // orange-500
  good: '#4ade80', // green-400
  goodAccent: '#16a34a', // green-600
  text: '#cbd5e1', // slate-300
  grid: 'rgba(203, 213, 225, 0.2)',
};

export const KPI_ICONS: Record<string, string> = {
  acceptance: "M9 12.75L11.25 15 15 9.75M21 12a9 9 0 11-18 0 9 9 0 0118 0z",
  velocity: "M13.5 4.5L21 12m0 0l-7.5 7.5M21 12H3",
  reliability: "M9 12.75L11.25 15 15 9.75M21 12a9 9 0 11-18 0 9 9 0 0118 0z",
  defects: BUG_ICON,
  density: BUG_ICON,
  leakage: BUG_ICON,
  effectiveness: "M9 12.75L11.25 15 15 9.75M21 12a9 9 0 11-18 0 9 9 0 0118 0z",
  coverage: "M3.375 19.5h17.25m-17.25 0a1.125 1.125 0 01-1.125-1.125v-1.5c0-.621.504-1.125 1.125-1.125H6.75m0 0h10.5m-10.5 0H3.375m0 0s-.375-.188-.375-.375m17.25 0s-.375-.188-.375-.375m0 0H6.75m10.5 0v-1.5c0-.621-.504-1.125-1.125-1.125H8.25m10.5 0v-1.5a1.125 1.125 0 00-1.125-1.125H8.25m-6 3.75v-1.5c0-.621.504-1.125 1.125-1.125h1.5",
  functional: "M4.5 12.75l6 6 9-13.5",
  configurations: "M9.594 3.94c.09-.542.56-.94 1.11-.94h2.593c.55 0 1.02.398 1.11.94l.213 1.281c.063.374.313.686.645.87.074.04.147.083.22.127.324.196.72.257 1.075.124l1.217-.456a1.125 1.125 0 011.37.49l1.296 2.247a1.125 1.125 0 01-.26 1.431l-1.003.827c-.293.24-.438.613-.431.992a6.759 6.759 0 010 .255c-.007.378.138.75.43.99l1.005.828c.424.35.534.954.26 1.43l-1.298 2.247a1.125 1.125 0 01-1.369.491l-1.217-.456c-.355-.133-.75-.072-1.076.124a6.57 6.57 0 01-.22.128c-.331.183-.581.495-.644.869l-.213 1.28c-.09.543-.56.941-1.11.941h-2.594c-.55 0-1.02-.398-1.11-.94l-.213-1.281c-.062-.374-.312-.686-.644-.87a6.52 6.52 0 01-.22-.127c-.325-.196-.72-.257-1.076-.124l-1.217.456a1.125 1.125 0 01-1.369-.49l-1.297-2.247a1.125 1.125 0 01.26-1.431l1.004-.827c.292-.24.437-.613.43-.992a6.932 6.932 0 010-.255c.007-.378-.138-.75-.43-.99l-1.004-.828a1.125 1.125 0 01-.26-1.43l1.297-2.247a1.125 1.125 0 011.37-.491l1.216.456c.356.133.751.072 1.076-.124.072-.044.146-.087.22-.128.332-.183.582-.495.644-.869l.214-1.281z M15 12a3 3 0 11-6 0 3 3 0 016 0z",
  trend: "M2.25 18L9 11.25l4.306 4.307a11.95 11.95 0 015.814-5.519l2.74-1.22m0 0l-5.94-2.28m5.94 2.28l-2.28 5.941",
  sprint: "M3.75 13.5l10.5-11.25L12 10.5h8.25L9.75 21.75 12 13.5H3.75z",
  backlog: "M3 4.5h14.25M3 9h14.25M3 13.5h14.25m-14.25 4.5h14.25",
  storyPoints: "M3 4.5h14.25M3 9h14.25M3 13.5h14.25m-14.25 4.5h14.25",
  team: "M18 18.72a9.094 9.094 0 003.741-.479 1.988 1.988 0 00-1.549-3.593 9.426 9.426 0 01-2.192-.47c-.552-.163-1.138-.488-1.639-.933a7.47 7.47 0 01-2.12-3.513c-.126-.44-.25-1.01-.25-1.549 0-.54.124-1.11.25-1.55a7.47 7.47 0 012.12-3.513c.5-.444 1.087-.77 1.64-.932a9.426 9.426 0 012.192-.47 1.988 1.988 0 001.548-3.593A9.094 9.094 0 0018 1.28c-5.42 0-9.817 4.21-9.817 9.42S12.58 20.12 18 20.12a9.094 9.094 0 003.741-1.4zM2.25 10.7c0 5.21 4.397 9.42 9.817 9.42a9.094 9.094 0 003.741-1.4 1.988 1.988 0 00-1.549-3.593 9.426 9.426 0 01-2.192-.47c-.552-.163-1.138-.488-1.639-.933a7.47 7.47 0 01-2.12-3.513c-.126-.44-.25-1.01-.25-1.549 0-.54.124-1.11.25-1.55a7.47 7.47 0 012.12-3.513c.5-.444 1.087-.77 1.64-.932a9.426 9.426 0 012.192-.47 1.988 1.988 0 001.548-3.593A9.094 9.094 0 0012.067 1.28c-5.42 0-9.817 4.21-9.817 9.42z"
};