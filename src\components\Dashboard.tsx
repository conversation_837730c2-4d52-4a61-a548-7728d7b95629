import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { AzureDevOpsService } from '../services/api';
import { MetricTab, type Credentials, type DashboardConfig, type TabProps } from '../types';
import { ALL_METRIC_TABS, TAB_ICONS } from '../constants';
import { OverviewTab } from './tabs/OverviewTab';
import { StoryPointAcceptanceTab } from './tabs/StoryPointAcceptanceTab';
import { SprintVelocityByMonthTab } from './tabs/SprintVelocityByMonthTab';
import { SprintVelocityBySprintTab } from './tabs/SprintVelocityBySprintTab';
import { DefectMetricsTab } from './tabs/DefectMetricsTab';
import { TestMetricsTab } from './tabs/TestMetricsTab';
import { BacklogHealthTab } from './tabs/BacklogHealthTab';
import { LoadingSpinner } from './common/LoadingSpinner';
import { ErrorDisplay } from './common/ErrorDisplay';

interface DashboardProps {
  credentials: Credentials;
  config: DashboardConfig;
  onLogout: () => void;
  onBack: () => void;
}

const SideNav: React.FC<{ activeTab: MetricTab; onSelectTab: (tab: MetricTab) => void; isExpanded: boolean; onToggle: () => void }> = ({ activeTab, onSelectTab, isExpanded, onToggle }) => (
  <aside className={`bg-slate-900/50 backdrop-blur-sm flex flex-col border-r border-slate-700 transition-all duration-300 ease-in-out ${isExpanded ? 'w-64' : 'w-20'}`}>
    <div className="flex-grow p-3 flex flex-col items-center space-y-4">
      {ALL_METRIC_TABS.map((tab) => (
        <button
          key={tab}
          title={tab}
          onClick={() => onSelectTab(tab)}
          className={`flex items-center h-14 w-full rounded-xl transition-colors duration-200 group overflow-hidden ${
            activeTab === tab
              ? 'bg-sky-500/20 text-sky-400'
              : 'text-slate-400 hover:bg-slate-700/50 hover:text-slate-200'
          } ${isExpanded ? 'justify-start px-4' : 'justify-center'}`}
        >
          <svg className="w-7 h-7 flex-shrink-0" fill="none" stroke="currentColor" strokeWidth={1.5} viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path strokeLinecap="round" strokeLinejoin="round" d={TAB_ICONS[tab]} />
          </svg>
          <span className={`text-sm font-medium whitespace-nowrap transition-all duration-200 ${isExpanded ? 'w-auto opacity-100 ml-4' : 'w-0 opacity-0 ml-0'}`}>{tab}</span>
        </button>
      ))}
    </div>
    <div className="p-3 border-t border-slate-700">
        <button onClick={onToggle} className={`flex items-center h-14 w-full rounded-xl text-slate-400 hover:bg-slate-700/50 hover:text-slate-200 transition-colors duration-200 group overflow-hidden ${isExpanded ? 'justify-start px-4' : 'justify-center'}`}>
            <svg className="w-7 h-7 flex-shrink-0" fill="none" stroke="currentColor" strokeWidth={1.5} viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" d={isExpanded ? "M18.75 19.5l-7.5-7.5 7.5-7.5m-6 15L5.25 12l7.5-7.5" : "M11.25 4.5l7.5 7.5-7.5 7.5m-6-15l7.5 7.5-7.5 7.5"} />
            </svg>
            <span className={`text-sm font-medium whitespace-nowrap transition-all duration-200 ${isExpanded ? 'w-auto opacity-100 ml-4' : 'w-0 opacity-0 ml-0'}`}>Collapse</span>
        </button>
    </div>
  </aside>
);

const UserMenu: React.FC<{ onLogout: () => void; onBack: () => void }> = ({ onLogout, onBack }) => {
    const [isOpen, setIsOpen] = useState(false);

    return (
        <div className="relative">
            <button onClick={() => setIsOpen(!isOpen)} className="w-10 h-10 flex items-center justify-center bg-slate-700 rounded-full hover:bg-slate-600 transition">
                <svg className="w-6 h-6 text-slate-300" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" /></svg>
            </button>
            {isOpen && (
                <div className="absolute right-0 mt-2 w-56 bg-slate-800 rounded-md shadow-lg py-1 z-10 border border-slate-700">
                    <button onClick={onBack} className="block w-full text-left px-4 py-2 text-sm text-slate-300 hover:bg-slate-700 transition">Change Project/Teams</button>
                    <button onClick={onLogout} className="block w-full text-left px-4 py-2 text-sm text-red-400 hover:bg-slate-700 transition">Logout</button>
                </div>
            )}
        </div>
    );
};

const tabComponents: Record<MetricTab, React.FC<TabProps>> = {
    [MetricTab.Overview]: OverviewTab,
    [MetricTab.StoryPointAcceptance]: StoryPointAcceptanceTab,
    [MetricTab.SprintVelocityByMonth]: SprintVelocityByMonthTab,
    [MetricTab.SprintVelocityBySprint]: SprintVelocityBySprintTab,
    [MetricTab.DefectMetrics]: DefectMetricsTab,
    [MetricTab.TestMetrics]: TestMetricsTab,
    [MetricTab.BacklogHealth]: BacklogHealthTab,
};

export const Dashboard: React.FC<DashboardProps> = ({ credentials, config, onLogout, onBack }) => {
  const [activeTab, setActiveTab] = useState<MetricTab>(MetricTab.Overview);
  const [loading, setLoading] = useState(true);
  const [loadingText, setLoadingText] = useState('Initializing...');
  const [error, setError] = useState<string | null>(null);
  const [data, setData] = useState<Record<string, any>>({});
  const [isSidebarExpanded, setIsSidebarExpanded] = useState(true);
  
  const { project, teams } = config;
  const apiService = useMemo(() => new AzureDevOpsService(credentials.organization, credentials.pat), [credentials]);

  const fetchData = useCallback(async () => {
    setLoading(true);
    setError(null);
    setData({});
    let currentStep = 'initializing...';

    try {
      currentStep = 'loading story point acceptance...';
      setLoadingText('Loading Story Point Acceptance...');
      const storyPointData = await apiService.getStoryPointAcceptance(project, teams);
      setData(prev => ({ ...prev, storyPointAcceptance: storyPointData }));

      currentStep = 'loading sprint velocity...';
      setLoadingText('Loading Sprint Velocity...');
      const velocityMonthData = await apiService.getSprintVelocityByMonth(project, teams);
      const velocitySprintData = await apiService.getSprintVelocityBySprint(project, teams);
      setData(prev => ({ ...prev, sprintVelocityByMonth: velocityMonthData, sprintVelocityBySprint: velocitySprintData }));
      
      currentStep = 'loading defect metrics...';
      setLoadingText('Loading Defect Metrics...');
      const defectData = await apiService.getDefectMetrics(project, teams);
      setData(prev => ({...prev, defectMetrics: defectData}));

      currentStep = 'loading test metrics...';
      setLoadingText('Loading Test Metrics...');
      const testData = await apiService.getEnhancedTestMetrics(project, teams, (message, progress) => {
        const progressText = progress ? ` (${progress}%)` : '';
        setLoadingText(`${message}${progressText}`);
      });
      setData(prev => ({...prev, testMetrics: testData}));
      
      currentStep = 'loading backlog health...';
      setLoadingText('Loading Backlog Health...');
      const backlogData = await apiService.getBacklogHealth(project, teams);
      setData(prev => ({...prev, backlogHealth: backlogData}));
      
      currentStep = 'loading future sprints...';
      setLoadingText('Loading Future Sprints...');
      const futureSprintsData: Record<string, any[]> = {};
      for (const team of teams) {
          try {
            futureSprintsData[team.name] = await apiService.getFutureIterations(project, team);
          } catch(e) {
            console.warn(`Could not fetch future sprints for team ${team.name}`, e);
            futureSprintsData[team.name] = [];
          }
      }
      setData(prev => ({...prev, futureSprints: futureSprintsData}));

    } catch (e) {
      const errorMessage = e instanceof Error ? e.message : 'An unknown error occurred';
      setError(`Failed while ${currentStep}: ${errorMessage}`);
    } finally {
      setLoading(false);
      setLoadingText('');
    }
  }, [apiService, project, teams]);

  useEffect(() => {
    fetchData();
  }, [fetchData]); 

  const ActiveTabComponent = tabComponents[activeTab];

  return (
    <div className="flex flex-col h-screen bg-transparent">
       <header className="flex items-center justify-between p-4 bg-slate-900/50 backdrop-blur-sm shadow-lg border-b border-slate-700 z-10">
          <div className="flex items-center space-x-3">
              <svg className="w-8 h-8 text-sky-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path></svg>
              <div className="flex flex-col">
                <h1 className="text-xl font-bold text-slate-100">Metrics Dashboard</h1>
                <span className="text-xs text-slate-400">Project: {project.name}</span>
              </div>
          </div>
          <UserMenu onLogout={onLogout} onBack={onBack} />
      </header>

      <div className="flex flex-1 overflow-hidden">
        <SideNav 
          activeTab={activeTab} 
          onSelectTab={setActiveTab}
          isExpanded={isSidebarExpanded}
          onToggle={() => setIsSidebarExpanded(!isSidebarExpanded)}
        />
        
        <main className="flex-1 overflow-y-auto p-6">
          {loading ? (
              <LoadingSpinner text={loadingText} />
          ) : error ? (
              <ErrorDisplay error={error} />
          ) : (
              <ActiveTabComponent data={data} teams={teams} project={project} />
          )}
        </main>
      </div>
    </div>
  );
};