#!/bin/bash

# Production Deployment Script
# This script builds and deploys the application

set -e

echo "🚀 Starting production deployment..."

# Clean previous build
echo "🧹 Cleaning previous build..."
npm run clean

# Install dependencies
echo "📦 Installing dependencies..."
npm ci

# Build the application
echo "🔨 Building application..."
npm run build

# Check if Docker is available
if command -v docker &> /dev/null; then
    echo "🐳 Building Docker image..."
    docker build -t azure-devops-dashboard:latest .

    echo "✅ Deployment ready!"
    echo "To run: docker run -p 3000:80 azure-devops-dashboard:latest"
else
    echo "✅ Build complete! Deploy the 'dist' folder to your hosting service."
    echo "To preview locally: npm run serve"
fi

echo "🎉 Deployment preparation complete!"
