// LIVE API SERVICE
// This file makes real calls to the Azure DevOps API.
import type { ChartData, Project, Team } from '../types';

// --- Configuration ---
// These are common values and assumptions for metric calculations.
const STORY_POINTS_FIELD = 'Microsoft.VSTS.Scheduling.StoryPoints';
const COMPLETED_STATES = ['Done', 'Closed', 'Signed Off'];
const REMOVED_STATES = ['Removed'];
const WORK_ITEM_TYPES = `'User Story', 'Bug'`;
const BUG_WORK_ITEM_TYPE = `'Bug'`;
const PRODUCTION_BUG_TAG = 'Production';

export class AzureDevOpsService {
  private organization: string;
  private pat: string;
  private baseUrl: string;
  
  // Cache to store team area path settings to reduce redundant API calls
  private teamAreaPathsCache: Map<string, any[]> = new Map();
  // Caches for test artifacts to avoid re-fetching the same suites/points across sprints
  private testSuitesCache: Map<number, any[]> = new Map(); // planId -> suites (flattened with children)
  private testPointsCache: Map<string, any[]> = new Map(); // `${planId}-${suiteId}` -> test points

  // Centralized API versions based on Azure DevOps REST API v7.2 documentation
  private apiVersions = {
    projects: '7.2-preview.4',
    teams: '7.2-preview.3',
    iterations: '7.2-preview.1',
    teamfieldvalues: '7.2-preview.1', // For fetching area paths
    wiql: '7.2-preview.2',
    workitemsbatch: '7.2-preview.1',
    testruns: '7.2-preview.3',
  // Use unified 7.1-preview.1 versions for modern Test Plan APIs
  testplans: '7.1-preview.1',
  testsuites: '7.1-preview.1',
  testpoints: '7.1-preview.1',
    testresults: '7.1',
    profile: '7.2-preview.3',
  };

  constructor(organization: string, pat: string) {
    this.organization = organization;
    this.pat = pat;
    this.baseUrl = `https://dev.azure.com/${this.organization}`;
  }

  private _getAuthHeader() {
    return `Basic ${btoa(`:${this.pat}`)}`;
  }

  private async _fetch(url: string, apiVersion: string, method = 'GET', body: any = null, headers: HeadersInit = {}, useSpsUrl = false) {
    const baseUrl = useSpsUrl ? `https://vssps.dev.azure.com/${this.organization}` : this.baseUrl;
    const fullUrl = `${baseUrl}${url}${url.includes('?') ? '&' : '?'}api-version=${apiVersion}`;
    
    const options: RequestInit = {
        method,
        headers: {
          'Authorization': this._getAuthHeader(),
          'Content-Type': 'application/json',
          ...headers,
        },
    };
    
    if (body) {
        options.body = JSON.stringify(body);
    }
    
    const response = await fetch(fullUrl, options);

    const contentType = response.headers.get('content-type');
    if (contentType && contentType.includes('text/html')) {
        throw new Error(`Authentication failed. The server returned an HTML page, which usually means the PAT is invalid, expired, or lacks permissions. Please verify your credentials.`);
    }

    if (!response.ok) {
      const errorData = await response.text();
      console.error(`Azure DevOps API error on URL: ${fullUrl}`, errorData);
      throw new Error(`Azure DevOps API error: ${response.status} ${response.statusText}. Check permissions or network.`);
    }
    if (response.status === 204) { // No Content
        return null;
    }
    return response.json();
  }

  public async verifyConnection(): Promise<boolean> {
    try {
      await this._fetch('/_apis/profile/profiles/me', this.apiVersions.profile, 'GET', null, {}, true);
      return true;
    } catch (e) {
      console.error("Connection verification failed", e);
      throw e;
    }
  }

  // --- Core API Helpers ---

  public async getProjects(): Promise<Project[]> {
    const response = await this._fetch('/_apis/projects', this.apiVersions.projects);
    if (!response || !response.value) return [];
    return response.value.map((p: any) => ({ id: p.id, name: p.name }));
  }

  public async getTeams(projectId: string): Promise<Team[]> {
    const response = await this._fetch(`/_apis/projects/${projectId}/teams`, this.apiVersions.teams);
    if (!response || !response.value) return [];
    return response.value.map((t: any) => ({ id: t.id, name: t.name }));
  }
  
  public async getFutureIterations(project: Project, team: Team): Promise<any[]> {
    const url = `/${project.id}/${team.id}/_apis/work/teamsettings/iterations`;
    const response = await this._fetch(url, this.apiVersions.iterations);
    
    const allIterations = response.value || [];
    if (!Array.isArray(allIterations)) {
        console.warn(`Unexpected response format for future iterations of team ${team.name}. Expected an array.`, response);
        return [];
    }
    
    const now = new Date();
    now.setHours(0, 0, 0, 0); // Set to start of day for comparison

    const futureIterations = allIterations.filter(iteration => {
        if (iteration.attributes && iteration.attributes.startDate) {
            const startDate = new Date(iteration.attributes.startDate);
            return !isNaN(startDate.getTime()) && startDate >= now;
        }
        return false;
    });

    futureIterations.sort((a, b) => {
        const dateA = new Date(a.attributes.startDate);
        const dateB = new Date(b.attributes.startDate);
        return dateA.getTime() - dateB.getTime();
    });

    return futureIterations.slice(0, 8); // Get up to 8 future sprints for the forecast
  }

  private async _getIterations(project: Project, team: Team): Promise<any[]> {
    // The `$timeframe` parameter can be unreliable in some Azure DevOps environments, causing errors.
    // To ensure compatibility, we fetch all iterations for the team and filter them on the client-side.
    const url = `/${project.id}/${team.id}/_apis/work/teamsettings/iterations`;
    const response = await this._fetch(url, this.apiVersions.iterations);
    
    const allIterations = response.value || [];
    if (!Array.isArray(allIterations)) {
        console.warn(`Unexpected response format for iterations of team ${team.name}. Expected an array.`, response);
        return [];
    }
    
    const now = new Date();

    const pastIterations = allIterations.filter(iteration => {
        // An iteration is considered "past" if it has a valid finishDate before the current time.
        if (iteration.attributes && iteration.attributes.finishDate) {
            const finishDate = new Date(iteration.attributes.finishDate);
            return !isNaN(finishDate.getTime()) && finishDate < now;
        }
        return false;
    });

    // The API doesn't guarantee order, so we sort by finish date descending to get the most recent sprints first.
    pastIterations.sort((a, b) => {
        const dateA = new Date(a.attributes.finishDate);
        const dateB = new Date(b.attributes.finishDate);
        return dateB.getTime() - dateA.getTime();
    });

    // Return up to the latest 12 past sprints for analysis.
    return pastIterations.slice(0, 12);
  }

  // Fetches a team's configured area paths. This is the most reliable way to filter.
  private async _getTeamAreaPathsInfo(project: Project, team: Team): Promise<any[]> {
    if (this.teamAreaPathsCache.has(team.id)) {
      return this.teamAreaPathsCache.get(team.id)!;
    }
    // Use IDs for robustness
    const url = `/${project.id}/${team.id}/_apis/work/teamsettings/teamfieldvalues`;
    const response = await this._fetch(url, this.apiVersions.teamfieldvalues);
    const areaPaths = response.values || [];
    this.teamAreaPathsCache.set(team.id, areaPaths);
    return areaPaths;
  }

  // Builds a valid WIQL Area Path clause from the team's settings.
  private _getAreaPathClause(areaPathsInfo: any[]): string {
    if (!areaPathsInfo || areaPathsInfo.length === 0) {
      return '1=0';
    }

    const clauses = areaPathsInfo.map((area: { value: string; includeChildren: boolean; }) => {
      const escapedPath = area.value.replace(/'/g, "''").replace(/\\/g, '\\\\');
      if (area.includeChildren) {
        return `[System.AreaPath] UNDER '${escapedPath}'`;
      } else {
        return `[System.AreaPath] = '${escapedPath}'`;
      }
    });

    return `(${clauses.join(' OR ')})`;
  }
  
  private async _runWiql(project: Project, query: string): Promise<any> {
    const url = `/${project.id}/_apis/wit/wiql`;
    return this._fetch(url, this.apiVersions.wiql, 'POST', { query });
  }

 private async _getWorkItemDetails(project: Project, ids: number[], fields: string[]): Promise<any[]> {
    if (ids.length === 0) return [];

    const BATCH_SIZE = 200; // Azure DevOps API limit
    const idChunks: number[][] = [];

    for (let i = 0; i < ids.length; i += BATCH_SIZE) {
        idChunks.push(ids.slice(i, i + BATCH_SIZE));
    }
    
    const url = `/${project.id}/_apis/wit/workitemsbatch`;

    const batchPromises = idChunks.map(chunk => 
        this._fetch(url, this.apiVersions.workitemsbatch, 'POST', {
            ids: chunk,
            fields: ['System.Id', 'System.State', 'System.IterationPath', 'System.WorkItemType', 'System.Tags', ...fields],
        })
    );

    const batchResults = await Promise.all(batchPromises);

    return batchResults.flatMap(result => (result && result.value) ? result.value : []);
  }

  // --- Metric Calculation Methods ---

  private async _getSprintMetrics(project: Project, teams: Team[]) {
    const sprintMetrics: Record<string, any[]> = {};

    for (const team of teams) {
        sprintMetrics[team.name] = [];
        const iterations = await this._getIterations(project, team);
        if (iterations.length === 0) continue;
        
        const iterationPaths = iterations.map(iter => iter.path);
        if(iterationPaths.length === 0) continue;
        
        const iterationPathsClause = iterationPaths.map(p => `'${p.replace(/'/g, "''")}'`).join(',');
        
        const teamAreaPathsInfo = await this._getTeamAreaPathsInfo(project, team);
        const areaPathClause = this._getAreaPathClause(teamAreaPathsInfo);

        const wiql = `
            SELECT [System.Id]
            FROM WorkItems
            WHERE [System.WorkItemType] IN (${WORK_ITEM_TYPES})
            AND [System.TeamProject] = @project
            AND [System.IterationPath] IN (${iterationPathsClause})
            AND ${areaPathClause}
        `;
        
        const queryResult = await this._runWiql(project, wiql);
        const workItemIds = queryResult.workItems.map((wi: any) => wi.id);
        const workItems = await this._getWorkItemDetails(project, workItemIds, [STORY_POINTS_FIELD]);

        for (const iter of iterations) {
            const sprintWorkItems = workItems.filter(wi => wi.fields['System.IterationPath'] === iter.path);
            
            let completedStoryPoints = 0;
            let totalStoryPoints = 0;
            let removedStoryPoints = 0;

            for (const wi of sprintWorkItems) {
                const points = wi.fields[STORY_POINTS_FIELD] || 0;
                if (COMPLETED_STATES.includes(wi.fields['System.State'])) {
                    completedStoryPoints += points;
                } else if (REMOVED_STATES.includes(wi.fields['System.State'])) {
                    removedStoryPoints += points;
                }
                totalStoryPoints += points;
            }
            
            const committedButNotRemoved = totalStoryPoints - removedStoryPoints;

            sprintMetrics[team.name].push({
                name: iter.name,
                path: iter.path,
                startDate: new Date(iter.attributes.startDate),
                finishDate: new Date(iter.attributes.finishDate),
                acceptance: committedButNotRemoved > 0 ? (completedStoryPoints / committedButNotRemoved) * 100 : 100,
                completed: completedStoryPoints,
                committed: totalStoryPoints,
                removed: removedStoryPoints,
            });
        }
    }
    return sprintMetrics;
  }

  public async getStoryPointAcceptance(project: Project, teams: Team[]): Promise<Record<string, ChartData[]>> {
    const sprintMetrics = await this._getSprintMetrics(project, teams);
    const monthlyData: Record<string, ChartData[]> = {};

    for(const teamName in sprintMetrics) {
        const monthMap: Record<string, { totalAcceptance: number, count: number, displayName: string }> = {};
        
        sprintMetrics[teamName].forEach(sprint => {
            const finishDate = new Date(sprint.finishDate);
            const monthKey = `${finishDate.getUTCFullYear()}-${String(finishDate.getUTCMonth()).padStart(2, '0')}`;
            
            if (!monthMap[monthKey]) {
                const monthName = finishDate.toLocaleString('default', { month: 'long', timeZone: 'UTC' });
                const yearName = finishDate.getUTCFullYear();
                monthMap[monthKey] = { totalAcceptance: 0, count: 0, displayName: `${monthName} ${yearName}` };
            }
            monthMap[monthKey].totalAcceptance += sprint.acceptance;
            monthMap[monthKey].count++;
        });

        const sortedMonthlyData = Object.entries(monthMap).map(([key, data]) => ({
            key,
            name: data.displayName.split(' ')[0].substring(0, 3),
            Month: data.displayName,
            Acceptance: data.totalAcceptance / data.count
        })).sort((a,b) => a.key.localeCompare(b.key));
        
        monthlyData[teamName] = sortedMonthlyData.slice(-6);
    }
    return monthlyData;
  }
  
  public async getSprintVelocityByMonth(project: Project, teams: Team[]): Promise<Record<string, ChartData[]>> {
     const sprintMetrics = await this._getSprintMetrics(project, teams);
     const monthlyData: Record<string, ChartData[]> = {};

     for (const teamName in sprintMetrics) {
        const monthMap: Record<string, { completed: number, committed: number, removed: number, displayName: string }> = {};

        sprintMetrics[teamName].forEach(sprint => {
            const finishDate = new Date(sprint.finishDate);
            const monthKey = `${finishDate.getUTCFullYear()}-${String(finishDate.getUTCMonth()).padStart(2, '0')}`;
            
            if (!monthMap[monthKey]) {
                const monthName = finishDate.toLocaleString('default', { month: 'long', timeZone: 'UTC' });
                const yearName = finishDate.getUTCFullYear();
                monthMap[monthKey] = { completed: 0, committed: 0, removed: 0, displayName: `${monthName} ${yearName}` };
            }
            monthMap[monthKey].completed += sprint.completed;
            monthMap[monthKey].committed += sprint.committed;
            monthMap[monthKey].removed += sprint.removed;
        });

        const sortedMonthlyData = Object.entries(monthMap).map(([key, data]) => ({
            key,
            name: data.displayName.split(' ')[0].substring(0, 3),
            Month: data.displayName,
            Completed: data.completed,
            Committed: data.committed,
            Removed: data.removed,
        })).sort((a,b) => a.key.localeCompare(b.key));
        
        monthlyData[teamName] = sortedMonthlyData.slice(-6);
     }
     return monthlyData;
  }

  public async getSprintVelocityBySprint(project: Project, teams: Team[]): Promise<Record<string, ChartData[]>> {
    const sprintMetrics = await this._getSprintMetrics(project, teams);
    const sprintData: Record<string, ChartData[]> = {};

    for (const teamName in sprintMetrics) {
        sprintData[teamName] = sprintMetrics[teamName]
            .map(sprint => ({
                name: sprint.name,
                Completed: sprint.completed,
                Committed: sprint.committed,
                Removed: sprint.removed,
            }))
            .reverse(); // Reverse to show oldest sprint first (chronological)
    }
    return sprintData;
  }

  public async getDefectMetrics(project: Project, teams: Team[]): Promise<Record<string, { rejection: ChartData[], density: ChartData[], leakage: ChartData[] }>> {
    const defectData: Record<string, any> = {};
    const sprintMetrics = await this._getSprintMetrics(project, teams);

    for (const team of teams) {
        const teamSprints = sprintMetrics[team.name] || [];
        if (teamSprints.length === 0) continue;
        
        const iterationPaths = teamSprints.map(s => s.path);
        const iterationPathsClause = iterationPaths.map(p => `'${p.replace(/'/g, "''")}'`).join(',');
        
        const teamAreaPathsInfo = await this._getTeamAreaPathsInfo(project, team);
        const areaPathClause = this._getAreaPathClause(teamAreaPathsInfo);
        
        const wiql = `
            SELECT [System.Id]
            FROM WorkItems
            WHERE [System.WorkItemType] = 'Bug'
            AND [System.TeamProject] = @project
            AND [System.State] <> 'Removed'
            AND [System.IterationPath] IN (${iterationPathsClause})
            AND ${areaPathClause}
        `;
        const queryResult = await this._runWiql(project, wiql);
        const bugIds = queryResult.workItems.map((wi: any) => wi.id);
        const bugs = await this._getWorkItemDetails(project, bugIds, []);

        const metrics = { rejection: [] as ChartData[], density: [] as ChartData[], leakage: [] as ChartData[] };

        for (const sprint of teamSprints) {
            const sprintBugs = bugs.filter(b => b.fields['System.IterationPath'] === sprint.path);
            const bugCount = sprintBugs.length;
            
            const completedPoints = sprint.completed;
            metrics.density.push({ 
                name: sprint.name, 
                Density: completedPoints > 0 ? bugCount / completedPoints : 0,
                'Defect Count': bugCount,
                'Story Points': completedPoints
            });

            const prodBugs = sprintBugs.filter(b => b.fields['System.Tags'] && b.fields['System.Tags'].includes(PRODUCTION_BUG_TAG)).length;
            metrics.leakage.push({ name: sprint.name, 'Testing Defects Count': bugCount - prodBugs, 'Production Defects Count': prodBugs });
            
            metrics.rejection.push({ 
                name: sprint.name, 
                'Testing Defects': bugCount, 
                'Rejection Ratio (%)': 0,
                'Story Points': completedPoints
            });
        }
        
        // Reverse for chronological order
        metrics.rejection.reverse();
        metrics.density.reverse();
        metrics.leakage.reverse();

        defectData[team.name] = metrics;
    }
    return defectData;
  }
  
  public async getTestMetrics(project: Project, teams: Team[]): Promise<{ effectiveness: Record<string, ChartData[]>, coverage: ChartData[] }> {
    const effectiveness: Record<string, ChartData[]> = {};
    const sprintMetrics = await this._getSprintMetrics(project, teams);

     for (const team of teams) {
        const teamSprints = sprintMetrics[team.name] || [];
        if (teamSprints.length === 0) continue;

        const iterationPaths = teamSprints.map(s => s.path);
        const iterationPathsClause = iterationPaths.map(p => `'${p.replace(/'/g, "''")}'`).join(',');
        
        const teamAreaPathsInfo = await this._getTeamAreaPathsInfo(project, team);
        const areaPathClause = this._getAreaPathClause(teamAreaPathsInfo);

        const wiql = `
          SELECT [System.Id] FROM WorkItems 
          WHERE [System.WorkItemType] = 'Bug' 
          AND [System.TeamProject] = @project
          AND [System.IterationPath] IN (${iterationPathsClause})
          AND ${areaPathClause}
        `;

        const queryResult = await this._runWiql(project, wiql);
        const bugIds = queryResult.workItems.map((wi: any) => wi.id);
        const bugs = await this._getWorkItemDetails(project, bugIds, []);

        effectiveness[team.name] = teamSprints.map(sprint => {
           const sprintBugs = bugs.filter(b => b.fields['System.IterationPath'] === sprint.path);
           return {
               name: sprint.name,
               'Testing Defects Count': sprintBugs.length,
               'Test Effectiveness': 100,
           }
        }).reverse(); // Reverse for chronological order
     }
    
    const sixMonthsAgo = new Date();
    sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 6);
    const runsResponse = await this._fetch(`/${project.id}/_apis/test/runs?minLastUpdatedDate=${sixMonthsAgo.toISOString()}`, this.apiVersions.testruns);
    const runs = runsResponse.value || [];

    const coverageMap: Record<string, { planned: number, executed: number }> = {};
    for (const run of runs) {
        if (!run.completedDate) continue;
        const monthKey = new Date(run.completedDate).toLocaleString('default', { month: 'long', year: 'numeric' });
        if (!coverageMap[monthKey]) coverageMap[monthKey] = { planned: 0, executed: 0 };
        
        const total = run.totalTests || 0;
        const incomplete = run.incompleteTests || 0;
        coverageMap[monthKey].executed += total - incomplete;
        coverageMap[monthKey].planned += total;
    }
    
    const coverage = Object.entries(coverageMap).map(([month, data]) => ({
      name: month.split(' ')[0].substring(0, 3),
      Month: month,
      'Test Cases Planned': data.planned,
      'Test Cases Executed': data.executed,
      'Functional Test Coverage': data.planned > 0 ? (data.executed / data.planned) * 100 : 0,
    })).sort((a,b) => new Date(a.Month).getTime() - new Date(b.Month).getTime());

    return { effectiveness, coverage };
  }

  // ============ NEW TEST COVERAGE IMPLEMENTATION ============
  
  // Get all test plans for a project
  private async _getTestPlans(project: Project): Promise<any[]> {
    try {
    // Correct endpoint: test plan APIs live under /testplan not /test
    const url = `/${project.id}/_apis/testplan/plans?state=active&includePlanDetails=true`;
      const response = await this._fetch(url, this.apiVersions.testplans);
      return response.value || [];
    } catch (error) {
      console.warn(`Failed to fetch test plans for project ${project.name}:`, error);
      return [];
    }
  }

  // Get test suites for a specific test plan (including nested suites)
  private async _getTestSuitesForPlan(project: Project, planId: number): Promise<any[]> {
    try {
    // Accept accidental object usage and coerce to numeric id if needed
    // (We previously passed the entire plan object which produced [object Object] 404s)
    const numericPlanId = typeof planId === 'number' ? planId : (planId as any)?.id ? Number((planId as any).id) : NaN;
    if (isNaN(numericPlanId)) {
      console.warn(`Invalid plan id supplied to _getTestSuitesForPlan:`, planId);
      return [];
    }
    // Return from cache if available
    if (this.testSuitesCache.has(numericPlanId)) {
      return this.testSuitesCache.get(numericPlanId)!;
    }
    // Correct endpoint base: /testplan/Plans/{planId}/suites
    const url = `/${project.id}/_apis/testplan/Plans/${numericPlanId}/suites?expand=children`;
      const response = await this._fetch(url, this.apiVersions.testsuites);
      const allSuites = response.value || [];
      
      console.log(`Plan ${numericPlanId}: Retrieved ${allSuites.length} top-level suites from API`);
      
      // Get nested suites recursively
      const nestedSuites = await this._getAllNestedSuites(project, numericPlanId, allSuites);
      
      console.log(`Plan ${numericPlanId}: Found ${allSuites.length} top-level suites, ${nestedSuites.length} total including nested`);
      this.testSuitesCache.set(numericPlanId, nestedSuites);
      return nestedSuites;
    } catch (error) {
      console.warn(`Failed to fetch test suites for plan ${planId}:`, error);
      return [];
    }
  }

  // Recursively get all nested test suites
  private async _getAllNestedSuites(project: Project, planId: number, suites: any[]): Promise<any[]> {
    const allSuites = [...suites];
    
    for (const suite of suites) {
      console.log(`Examining suite "${suite.name}" (ID: ${suite.id}) - hasChildren: ${suite.hasChildren}, suiteType: ${suite.suiteType}`);
      
      // Check for child suites using multiple approaches
      if (suite.hasChildren || suite.suiteType === 'StaticTestSuite' || suite.childSuiteCount > 0) {
        try {
          const childSuitesUrl = `/${project.id}/_apis/testplan/Plans/${planId}/suites/${suite.id}/suites`;
          const childResponse = await this._fetch(childSuitesUrl, this.apiVersions.testsuites);
          const childSuites = childResponse.value || [];
          
          if (childSuites.length > 0) {
            console.log(`Suite ${suite.id} (${suite.name}) has ${childSuites.length} child suites`);
            // Recursively get nested suites
            const nestedChildSuites = await this._getAllNestedSuites(project, planId, childSuites);
            allSuites.push(...nestedChildSuites);
          } else {
            console.log(`Suite ${suite.id} (${suite.name}) marked as having children but none found`);
          }
        } catch (error) {
          console.warn(`Failed to fetch child suites for suite ${suite.id}:`, error);
        }
      }
    }
    
    return allSuites;
  }

  // Get test points for a specific test suite (including all configurations)
  // Using dual API approach matching PowerShell script for better accuracy
  private async _getTestPointsForSuite(project: Project, planId: number, suiteId: number): Promise<any[]> {
    try {
      // Coerce potential object parameters (defensive)
      const numericPlanId = typeof planId === 'number' ? planId : (planId as any)?.id ? Number((planId as any).id) : NaN;
      const numericSuiteId = typeof suiteId === 'number' ? suiteId : (suiteId as any)?.id ? Number((suiteId as any).id) : NaN;
      if (isNaN(numericPlanId) || isNaN(numericSuiteId)) {
        console.warn(`Invalid ids supplied to _getTestPointsForSuite planId=${planId} suiteId=${suiteId}`);
        return [];
      }
      
      const cacheKey = `${numericPlanId}-${numericSuiteId}`;
      if (this.testPointsCache.has(cacheKey)) {
        return this.testPointsCache.get(cacheKey)!;
      }

      let testPoints: any[] = [];

      // Debug flag for specific test plan debugging
      const isDebugPlan = numericPlanId.toString() === '1914183' || numericPlanId.toString() === '1914183'; // Add your test plan ID here
      
      try {
        // First try: Legacy API endpoint (matches PowerShell script)
        const legacyUrl = `/${project.id}/_apis/test/Plans/${numericPlanId}/Suites/${numericSuiteId}/points?includePointDetails=true&$top=1000`;
        
        if (isDebugPlan) {
          console.log(`🐛 DEBUG: Calling legacy API: ${legacyUrl}`);
        }
        
        const legacyResponse = await this._fetch(legacyUrl, '5.0');
        testPoints = legacyResponse.value || [];
        console.log(`Suite ${numericSuiteId}: Legacy API retrieved ${testPoints.length} points`);
        
        if (isDebugPlan && testPoints.length > 0) {
          console.log(`🐛 DEBUG: Sample legacy response point:`, JSON.stringify(testPoints[0], null, 2));
        }
        
      } catch (legacyError) {
        console.log(`Suite ${numericSuiteId}: Legacy API failed (${legacyError.message}), trying modern API`);
        
        try {
          // Fallback: Modern API endpoint (testplan namespace)
          const modernUrl = `/${project.id}/_apis/testplan/Plans/${numericPlanId}/Suites/${numericSuiteId}/TestPoint?includePointDetails=true&$top=1000`;
          
          if (isDebugPlan) {
            console.log(`🐛 DEBUG: Calling modern API: ${modernUrl}`);
          }
          
          const modernResponse = await this._fetch(modernUrl, '7.1-preview.2');
          testPoints = modernResponse.value || [];
          console.log(`Suite ${numericSuiteId}: Modern API retrieved ${testPoints.length} points`);
          
          if (isDebugPlan && testPoints.length > 0) {
            console.log(`🐛 DEBUG: Sample modern response point:`, JSON.stringify(testPoints[0], null, 2));
          }
          
        } catch (modernError) {
          console.log(`Suite ${numericSuiteId}: Both APIs failed, trying query endpoint`);
          
          // Final fallback: Query endpoint
          const queryUrl = `/${project.id}/_apis/test/Points?planId=${numericPlanId}&suiteId=${numericSuiteId}&includePointDetails=true&$top=1000`;
          
          if (isDebugPlan) {
            console.log(`🐛 DEBUG: Calling query API: ${queryUrl}`);
          }
          
          const queryResponse = await this._fetch(queryUrl, this.apiVersions.testpoints);
          testPoints = queryResponse.value || [];
          console.log(`Suite ${numericSuiteId}: Query API retrieved ${testPoints.length} points`);
          
          if (isDebugPlan && testPoints.length > 0) {
            console.log(`🐛 DEBUG: Sample query response point:`, JSON.stringify(testPoints[0], null, 2));
          }
        }
      }

      const enriched = testPoints.map((p: any, index: number) => {
        // Extract outcome from various possible locations
        const outcome = p.outcome || 
                       p.mostRecentResultOutcome || 
                       p.mostRecentResult?.outcome || 
                       p.lastTestRun?.outcome ||
                       p.results?.[0]?.outcome || 
                       'None';
        
        const normalized = this._normalizeTestOutcome(outcome);
        
        // Match PowerShell script execution logic
        const isExecuted = outcome && 
                          outcome !== "" && 
                          outcome !== "Unspecified" && 
                          outcome !== "None" && 
                          outcome !== "NotRun";
        
        const enrichedPoint = {
          ...p,
          testCaseId: p.testCase?.id || p.testCaseReference?.id,
          configuration: p.configuration || { name: 'Default' },
          outcome: normalized,
          originalOutcome: outcome,
          isExecuted: isExecuted
        };
        
        // Debug first few points for specific plan
        if (isDebugPlan && index < 3) {
          console.log(`🐛 DEBUG Point ${index + 1}:`, {
            testCaseId: enrichedPoint.testCaseId,
            configuration: enrichedPoint.configuration?.name,
            originalOutcome: outcome,
            normalizedOutcome: normalized,
            isExecuted: isExecuted,
            rawPoint: {
              outcome: p.outcome,
              mostRecentResultOutcome: p.mostRecentResultOutcome,
              mostRecentResult: p.mostRecentResult,
              lastTestRun: p.lastTestRun,
              results: p.results
            }
          });
        }
        
        return enrichedPoint;
      });

      // Enhanced breakdown log matching PowerShell script logic
      const executed = enriched.filter(e => e.isExecuted).length;
      const passed = enriched.filter(e => e.outcome === 'Passed').length;
      const failed = enriched.filter(e => e.outcome === 'Failed').length;
      const blocked = enriched.filter(e => e.outcome === 'Blocked').length;
      const notRun = enriched.filter(e => !e.isExecuted).length;
      
      console.log(`Suite ${numericSuiteId} summary: Total=${enriched.length} Executed=${executed} (Passed=${passed} Failed=${failed} Blocked=${blocked}) NotRun=${notRun}`);
      
      // Debug: Show unique original outcomes
      const originalOutcomes = new Set(enriched.map(e => e.originalOutcome).filter(o => o));
      if (originalOutcomes.size > 0) {
        console.log(`Suite ${numericSuiteId} original outcomes:`, Array.from(originalOutcomes).sort());
      }

      this.testPointsCache.set(cacheKey, enriched);
      return enriched;
    } catch (error) {
      console.warn(`Failed to fetch test points for suite ${suiteId}:`, error);
      return [];
    }
  }

  // Check if two date ranges overlap
  private _dateRangesOverlap(start1: Date, end1: Date, start2: Date, end2: Date): boolean {
    return start1 <= end2 && start2 <= end1;
  }

  // Check if a date is within the last 3 months
  private _isWithinLastThreeMonths(date: Date | string): boolean {
    if (!date) return false;
    
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    if (isNaN(dateObj.getTime())) return false;
    
    const threeMonthsAgo = new Date();
    threeMonthsAgo.setMonth(threeMonthsAgo.getMonth() - 3);
    
    return dateObj >= threeMonthsAgo;
  }

  // Map test plans to sprints using multiple strategies
  private async _mapTestPlansToSprints(testPlans: any[], sprints: any[]): Promise<Map<string, any[]>> {
    const mapping = new Map<string, any[]>();
    
    console.log(`🔍 DEBUG: Mapping ${testPlans.length} test plans to ${sprints.length} sprints`);
    console.log(`🔍 Test plan samples:`, testPlans.slice(0, 2).map(p => ({
      id: p.id,
      name: p.name,
      iteration: p.iteration,
      startDate: p.startDate,
      endDate: p.endDate
    })));
    console.log(`🔍 Sprint samples:`, sprints.slice(0, 2).map(s => ({
      name: s.name,
      path: s.path,
      attributes: s.attributes
    })));
    
    for (const sprint of sprints) {
      const associatedPlans = testPlans.filter(plan => {
        // Strategy 1: Direct iteration path matching
        if (plan.iteration && plan.iteration === sprint.path) {
          console.log(`✅ Plan "${plan.name}" matched sprint "${sprint.name}" by iteration path`);
          return true;
        }
        
        // Strategy 2: Sprint name matching in test plan name (more flexible)
        if (plan.name && sprint.name) {
          const planName = plan.name.toLowerCase();
          const sprintName = sprint.name.toLowerCase();
          
          // Check if sprint name is in plan name OR plan name contains sprint number
          const sprintNumber = sprint.name.match(/\d+/)?.[0];
          if (planName.includes(sprintName) || 
              (sprintNumber && planName.includes(sprintNumber))) {
            console.log(`✅ Plan "${plan.name}" matched sprint "${sprint.name}" by name matching`);
            return true;
          }
        }
        
        // Strategy 3: Date range overlap (if dates are available)
        if (plan.startDate && plan.endDate && sprint.attributes?.startDate && sprint.attributes?.finishDate) {
          try {
            const planStart = new Date(plan.startDate);
            const planEnd = new Date(plan.endDate);
            const sprintStart = new Date(sprint.attributes.startDate);
            const sprintEnd = new Date(sprint.attributes.finishDate);
            
            if (this._dateRangesOverlap(planStart, planEnd, sprintStart, sprintEnd)) {
              console.log(`✅ Plan "${plan.name}" matched sprint "${sprint.name}" by date overlap`);
              return true;
            }
          } catch (dateError) {
            console.warn(`Date parsing error for plan ${plan.name} and sprint ${sprint.name}:`, dateError);
          }
        }
        
        // Strategy 4: Fallback - if no clear mapping, associate all plans with all sprints
        // This ensures we don't lose test data due to mapping failures
        return false;
      });
      
      // If no plans matched any sprint and this is a recent sprint, associate all plans
      if (associatedPlans.length === 0 && sprints.indexOf(sprint) < 6) {
        console.log(`🔄 No plans matched sprint "${sprint.name}" - using fallback strategy`);
        associatedPlans.push(...testPlans);
      }
      
      if (associatedPlans.length > 0) {
        console.log(`📋 Sprint "${sprint.name}" associated with ${associatedPlans.length} test plans: ${associatedPlans.map(p => p.name).join(', ')}`);
      }
      
      mapping.set(sprint.path, associatedPlans);
    }
    
    return mapping;
  }

  // Filter test points by team area paths
  private _filterTestPointsByArea(testPoints: any[], teamAreaPaths: any[]): any[] {
    if (!teamAreaPaths || teamAreaPaths.length === 0) {
      console.log(`No area path filtering applied - returning all ${testPoints.length} test points`);
      return testPoints;
    }
    
    // For comprehensive filtering, we would need to:
    // 1. Get test case details for each test point
    // 2. Check if test case area path matches team area paths
    // 
    // For now, return all test points since filtering at test point level
    // requires additional API calls that might be expensive
    console.log(`Area path filtering available but not applied - returning all ${testPoints.length} test points`);
    console.log(`Team area paths available: ${teamAreaPaths.map(ap => ap.value).join(', ')}`);
    
    return testPoints;
  }

  // Calculate test coverage for a specific sprint
  // Normalize test outcomes from Azure DevOps
  private _normalizeTestOutcome(outcome: string | null | undefined): string {
    if (!outcome) return 'Not Run';
    
    const normalized = outcome.toLowerCase().trim();
    
    switch (normalized) {
      case 'passed':
      case 'pass':
        return 'Passed';
      case 'failed':
      case 'fail':
      case 'error':
      case 'timeout':
        return 'Failed';
      case 'blocked':
      case 'notapplicable':
      case 'inconclusive':
        return 'Blocked';
      case 'notrun':
      case 'not run':
      case 'unspecified':
      case 'none':
      case '':
        return 'Not Run';
      default:
        console.log(`⚠️  Unknown test outcome: "${outcome}", treating as Not Run`);
        return 'Not Run';
    }
  }

  private async _calculateSprintTestCoverage(
    project: Project, 
    team: Team, 
    sprint: any, 
    testPlans: any[]
  ): Promise<ChartData> {
    console.log(`\n🔄 Calculating test coverage for sprint: ${sprint.name} (${team.name})`);
    console.log(`📋 Processing ${testPlans.length} test plans`);
    
    try {
      let totalTestCasesPlanned = 0;
      let totalTestCasesExecuted = 0;
      let totalPassed = 0;
      let totalFailed = 0;
      let totalBlocked = 0;
      let totalNotRun = 0;
      let totalConfigurations = 0;
      
      // Special handling for RJR-AEM-Testing
      const isRJRProject = project.name.includes('RJR') || project.name.includes('AEM');
      
  // Pre-calc sprint date bounds if available
  const sprintStart = sprint.attributes?.startDate ? new Date(sprint.attributes.startDate) : null;
  const sprintEnd = sprint.attributes?.finishDate ? new Date(sprint.attributes.finishDate) : null;

  // Outcome precedence so we keep the "best" execution state for a testCase+configuration combo within sprint window
  const precedence: Record<string, number> = { 'Not Run': 0, 'Blocked': 1, 'Failed': 2, 'Passed': 3 };

  // Map of unique case+config -> final normalized outcome for this sprint
  const comboOutcome = new Map<string, string>();

  for (const testPlan of testPlans) {
        console.log(`\n📊 Processing test plan: "${testPlan.name}" (ID: ${testPlan.id})`);
        
        // Get all test suites for this test plan
        const testSuites = await this._getTestSuitesForPlan(project, Number(testPlan.id));
        console.log(`📂 Found ${testSuites.length} test suites in plan "${testPlan.name}"`);
        
        // Process each test suite
        if (testSuites.length === 0) {
          console.log(`⚠️  No suites returned for test plan ${testPlan.id} (${testPlan.name}). Check permissions or plan structure.`);
          continue;
        }

        let planTestCasesCount = 0;
        let planPointsCount = 0;

        for (const suite of testSuites) {
          console.log(`\n  📁 Processing suite: "${suite.name}" (ID: ${suite.id}, Type: ${suite.suiteType || 'unknown'})`);
          
          // Get test points for this suite
          const testPoints = await this._getTestPointsForSuite(project, Number(testPlan.id), Number(suite.id));
          planPointsCount += testPoints.length;
          
          if (testPoints.length === 0) {
            console.log(`⚠️  Suite ${suite.id} (${suite.name}) produced zero test points.`);
            continue;
          }
          console.log(`    🎯 Found ${testPoints.length} test points in suite "${suite.name}"`);
          
          // Count unique test cases in this suite
          const uniqueTestCases = new Set(testPoints.map(p => p.testCaseId || p.testCase?.id).filter(id => id));
          planTestCasesCount += uniqueTestCases.size;
          console.log(`    📝 Suite contains ${uniqueTestCases.size} unique test cases`);
          
          // Filter test points to this sprint window (to avoid counting same plan across multiple sprints)
          // Make filtering more lenient to avoid missing test cases
          let pointsInSprint = testPoints;
          
          // Only apply date filtering if we have both sprint dates and the test points have date info
          if (sprintStart && sprintEnd) {
            const pointsWithDates = testPoints.filter(p => {
              const last = p.lastUpdatedDate || p.mostRecentResult?.completedDate || p.mostRecentResult?.startedDate;
              return last && !isNaN(new Date(last).getTime());
            });
            
            // If less than 50% of points have date info, skip date filtering to avoid losing data
            if (pointsWithDates.length >= testPoints.length * 0.5) {
              pointsInSprint = testPoints.filter(p => {
                const last = p.lastUpdatedDate || p.mostRecentResult?.completedDate || p.mostRecentResult?.startedDate;
                if (!last) return true; // Keep points without dates to be safe
                const d = new Date(last);
                return !isNaN(d.getTime()) && d >= sprintStart && d <= sprintEnd;
              });
            } else {
              console.log(`    ⚠️  Skipping date filtering - only ${pointsWithDates.length}/${testPoints.length} points have date info`);
            }
          }
          
          if (pointsInSprint.length !== testPoints.length) {
            console.log(`    ⏱️  Filtered ${testPoints.length - pointsInSprint.length} points outside sprint window (${pointsInSprint.length} remaining)`);
          }

          const localConfigurations = new Set<string>();
          const localTestCases = new Set<string>();
          
          pointsInSprint.forEach(p => {
            const configName = p.configuration?.name || 'Default';
            localConfigurations.add(configName);
            const testCaseId = p.testCaseId || p.testCase?.id;
            if (!testCaseId) {
              console.warn(`    ⚠️  Test point missing testCaseId:`, p);
              return;
            }
            localTestCases.add(testCaseId);
            const key = `${testCaseId}||${configName}`;
            const normalized = p.outcome; // Already normalized in enriched step
            const existing = comboOutcome.get(key);
            if (!existing || precedence[normalized] > precedence[existing]) {
              comboOutcome.set(key, normalized);
            }
          });
          
          console.log(`    📊 Suite "${suite.name}": ${localTestCases.size} unique test cases, ${localConfigurations.size} configurations, ${pointsInSprint.length} total points`);
          
          if (localConfigurations.size > 0) {
            totalConfigurations = Math.max(totalConfigurations, localConfigurations.size); // track max distinct configs encountered
          }
        }
        
        console.log(`\n📋 Test plan "${testPlan.name}" summary:`);
        console.log(`   📊 Total test points processed: ${planPointsCount}`);
        console.log(`   📝 Total unique test cases found: ${planTestCasesCount}`);
        console.log(`   ⚙️  Max configurations in a suite: ${totalConfigurations}`);
      }

      // Aggregate from comboOutcome (unique testCase + configuration within sprint)
      console.log(`\n📊 Final aggregation for sprint ${sprint.name}:`);
      console.log(`   🎯 Total unique test case+config combinations: ${comboOutcome.size}`);
      
      if (comboOutcome.size > 0) {
        totalTestCasesPlanned += comboOutcome.size; // planned = all combos appearing in sprint window
        
        // Debug: Show breakdown of outcomes
        const outcomeBreakdown = new Map<string, number>();
        comboOutcome.forEach(outcome => {
          outcomeBreakdown.set(outcome, (outcomeBreakdown.get(outcome) || 0) + 1);
        });
        console.log(`   📈 Outcome breakdown:`, Array.from(outcomeBreakdown.entries()));
        
        comboOutcome.forEach((outcome, key) => {
          if (outcome !== 'Not Run') {
            totalTestCasesExecuted++;
          }
          switch (outcome) {
            case 'Passed':
              totalPassed++; break;
            case 'Failed':
              totalFailed++; break;
            case 'Blocked':
              totalBlocked++; break;
            default:
              totalNotRun++; break;
          }
        });
      } else {
        console.log(`   ⚠️  No test case combinations found for sprint ${sprint.name}`);
      }

      // If still zero after processing plans, attempt fallback via test runs API scoped to sprint dates
      if (totalTestCasesPlanned === 0 && sprint.attributes?.startDate && sprint.attributes?.finishDate) {
        try {
          console.log(`🔁 Fallback: deriving metrics from test runs for sprint ${sprint.name}`);
          // Azure DevOps test runs API restricts date range to <= 7 days. Chunk the sprint range.
          const sprintStart = new Date(sprint.attributes.startDate);
          const sprintEnd = new Date(sprint.attributes.finishDate);
          const runs: any[] = [];
          const seenRunIds = new Set<number>();
          const MAX_WINDOW_DAYS = 6; // keep strictly < 7 to avoid 400 errors
          let windowStart = new Date(sprintStart);
          while (windowStart <= sprintEnd) {
            const windowEnd = new Date(Math.min(sprintEnd.getTime(), windowStart.getTime() + MAX_WINDOW_DAYS * 24 * 60 * 60 * 1000));
            const startIso = windowStart.toISOString();
            const endIso = windowEnd.toISOString();
            try {
              const windowResp = await this._fetch(`/${project.id}/_apis/test/runs?minLastUpdatedDate=${startIso}&maxLastUpdatedDate=${endIso}`, this.apiVersions.testruns);
              const windowRuns = windowResp.value || [];
              windowRuns.forEach((r: any) => { if (!seenRunIds.has(r.id)) { seenRunIds.add(r.id); runs.push(r); } });
              console.log(`   ⏱️  Collected ${windowRuns.length} runs for window ${startIso} -> ${endIso}`);
            } catch (chunkErr:any) {
              if (chunkErr?.message?.includes('Date Range')) {
                console.warn(`   ⚠️  Window fetch failed due to date range constraint even after chunking: ${chunkErr.message}`);
              } else {
                console.warn(`   ⚠️  Failed to fetch runs for window ${startIso} -> ${endIso}:`, chunkErr);
              }
            }
            // Advance to next day after current window end to avoid overlap
            windowStart = new Date(windowEnd.getTime() + 24 * 60 * 60 * 1000);
          }
          console.log(`   🧮 Aggregated ${runs.length} unique runs across sprint windows.`);
          let runPlanned = 0, runExecuted = 0, runPassed = 0, runFailed = 0, runBlocked = 0; 
          runs.forEach((run: any) => {
            const total = run.totalTests || 0;
            const incomplete = run.incompleteTests || 0;
            const passed = run.passedTests || 0;
            const unanalyzed = run.notApplicableTests || 0; // treat as blocked
            const failed = run.unanalyzedTests || (run.failedTests || 0); // depending on ADO payload
            runPlanned += total;
            runExecuted += total - incomplete;
            runPassed += passed;
            runFailed += failed;
            runBlocked += unanalyzed;
          });
          if (runPlanned > 0) {
            totalTestCasesPlanned = runPlanned;
            totalTestCasesExecuted = runExecuted;
            totalPassed = runPassed;
            totalFailed = runFailed;
            totalBlocked = runBlocked;
            console.log(`✅ Fallback applied using ${runs.length} runs: planned=${runPlanned} executed=${runExecuted}`);
          } else {
            console.log(`⚠️  Fallback runs also yielded zero totals.`);
          }
        } catch (fallbackErr) {
          console.warn(`Fallback test run aggregation failed:`, fallbackErr);
        }
      }
      
      // Apply user's rule: if planned = 0 then planned = executed (not the other way)
      if (totalTestCasesPlanned === 0) {
        console.log(`⚠️  No test cases planned detected for sprint ${sprint.name}. Applying rule planned = executed.`);
        if (totalTestCasesExecuted === 0 && (totalPassed + totalFailed + totalBlocked) > 0) {
          // Derive executed from result buckets if executed counter missed
            totalTestCasesExecuted = totalPassed + totalFailed + totalBlocked; // not counting Not Run
        }
        totalTestCasesPlanned = totalTestCasesExecuted;
      }

      // If still all zeros, add diagnostic marker
      if (totalTestCasesPlanned === 0 && totalTestCasesExecuted === 0) {
        console.log(`⚠️  Sprint ${sprint.name} has zero planned & executed after rule. Possible causes: no test plans, no suites, permissions, or API version mismatch.`);
      }
      
      // Calculate coverage percentage
      const functionalTestCoverage = totalTestCasesPlanned > 0 
        ? (totalTestCasesExecuted / totalTestCasesPlanned) * 100 
        : 0;
      
      // Calculate pass rate
      const passRate = totalTestCasesExecuted > 0 
        ? (totalPassed / totalTestCasesExecuted) * 100 
        : 0;
      
      console.log(`\n✅ Sprint ${sprint.name} Final Results:`);
      console.log(`   📊 Test Cases Planned: ${totalTestCasesPlanned}`);
      console.log(`   📊 Test Cases Executed: ${totalTestCasesExecuted}`);
      console.log(`   📊 Tests Passed: ${totalPassed}`);
      console.log(`   📊 Tests Failed: ${totalFailed}`);
      console.log(`   📊 Tests Blocked: ${totalBlocked}`);
      console.log(`   📊 Tests Not Run: ${totalNotRun}`);
      console.log(`   📊 Total Configurations: ${totalConfigurations}`);
      console.log(`   📊 Functional Test Coverage: ${functionalTestCoverage.toFixed(1)}%`);
      console.log(`   📊 Pass Rate: ${passRate.toFixed(1)}%`);
      
      return {
        name: sprint.name,
        sprintPath: sprint.path,
        'Test Cases Planned': totalTestCasesPlanned,
        'Test Cases Executed': totalTestCasesExecuted,
        'Tests Passed': totalPassed,
        'Tests Failed': totalFailed,
        'Tests Blocked': totalBlocked,
        'Tests Not Run': totalNotRun,
        'Functional Test Coverage': Math.round(functionalTestCoverage * 10) / 10,
        'Pass Rate': Math.round(passRate * 10) / 10,
        'Configurations': totalConfigurations
      };
      
    } catch (error) {
      console.error(`❌ Error calculating coverage for sprint ${sprint.name}:`, error);
      
      // Return empty/zero data - NO FAKE DATA
      return {
        name: sprint.name,
        sprintPath: sprint.path,
        'Test Cases Planned': 0,
        'Test Cases Executed': 0,
        'Tests Passed': 0,
        'Tests Failed': 0,
        'Tests Blocked': 0,
        'Tests Not Run': 0,
        'Functional Test Coverage': 0,
        'Pass Rate': 0,
        'Configurations': 0
      };
    }
  }

  // Main method to get functional test coverage with fallback
  public async getFunctionalTestCoverage(
    project: Project, 
    teams: Team[], 
    progressCallback?: (message: string, progress?: number) => void
  ): Promise<Record<string, ChartData[]>> {
    console.log(`🎯 Starting functional test coverage calculation for ${teams.length} selected teams`);
    console.log(`🎯 Selected teams: ${teams.map(t => t.name).join(', ')}`);
    
    try {
      return await this._calculateFunctionalTestCoverageInternal(project, teams, progressCallback);
    } catch (error) {
      console.error('❌ Functional test coverage calculation failed:', error);
      progressCallback?.('Error occurred during test coverage calculation', 100);
      // Return empty data structure for all teams - NO FAKE DATA
      return teams.reduce((acc, team) => {
        acc[team.name] = [];
        return acc;
      }, {} as Record<string, ChartData[]>);
    }
  }

  private async _calculateFunctionalTestCoverageInternal(
    project: Project, 
    teams: Team[], 
    progressCallback?: (message: string, progress?: number) => void
  ): Promise<Record<string, ChartData[]>> {
    const coverageData: Record<string, ChartData[]> = {};
    
    // Limit processing to prevent infinite loading - only process selected teams up to reasonable limit
    const maxTeamsToProcess = Math.min(teams.length, 3); // Process max 3 teams for performance
    const teamsToProcess = teams.slice(0, maxTeamsToProcess);
    
    if (teams.length > maxTeamsToProcess) {
      console.warn(`⚠️  Performance limit: Processing first ${maxTeamsToProcess} teams only (selected ${teams.length})`);
    }
    
    const startTime = Date.now();
    const estimatedTimePerTeam = 15000; // 15 seconds per team estimate
    const totalEstimatedTime = teamsToProcess.length * estimatedTimePerTeam;
    
    progressCallback?.(`Initializing test coverage calculation for ${teamsToProcess.length} teams... (Estimated: ${Math.round(totalEstimatedTime/1000)}s)`, 5);
    console.log(`\n🎯 Processing ${teamsToProcess.length} teams in project ${project.name}`);
    
    // Special handling for RJR-AEM-Testing project
    if (project.name.includes('RJR-AEM-Testing') || project.name.includes('AEM')) {
      console.log(`🔍 SPECIAL DEBUG MODE: Detected RJR-AEM-Testing project - enabling detailed logging`);
    }
    
    // Get test plans once for the entire project (not per team)
    console.log(`� Fetching test plans for project ${project.name}...`);
    const testPlans = await this._getTestPlans(project);
    console.log(`� Found ${testPlans.length} test plans for project ${project.name}`);
    
    if (testPlans.length === 0) {
      console.log(`⚠️  No active test plans found. Functional coverage likely to be zero. Check: Test Plans existence / PAT permissions / project selection.`);
      // Return empty data for all teams
      return teamsToProcess.reduce((acc, team) => {
        acc[team.name] = [];
        return acc;
      }, {} as Record<string, ChartData[]>);
    }
    
    for (let i = 0; i < teamsToProcess.length; i++) {
      const team = teamsToProcess[i];
      const baseProgress = Math.floor(20 + (i / teamsToProcess.length) * 70); // Progress from 20% to 90%
      
      try {
        const elapsedTime = Date.now() - startTime;
        const remainingTeams = teamsToProcess.length - i;
        const avgTimePerTeam = i > 0 ? elapsedTime / i : estimatedTimePerTeam;
        const estimatedRemainingTime = Math.round((remainingTeams * avgTimePerTeam) / 1000);
        
        progressCallback?.(`Processing team: ${team.name} (${i + 1}/${teamsToProcess.length}) - Est. ${estimatedRemainingTime}s remaining`, baseProgress);
        console.log(`\n📊 Processing team: ${team.name} (ID: ${team.id})`);
        
        // Step 1: Get team sprints  
        progressCallback?.(`Fetching sprints for team ${team.name}...`, baseProgress + 5);
        console.log(`📅 Fetching sprints for team ${team.name}...`);
        const sprints = await this._getIterations(project, team);
        console.log(`� Found ${sprints.length} sprints for team ${team.name}`);
        
        if (sprints.length === 0) {
          console.warn(`⚠️  No sprints found for team ${team.name}`);
          coverageData[team.name] = []; // Empty array, no fake data
          continue;
        }
        
        // Step 2: Associate plans to sprints one time to avoid redundant processing
        progressCallback?.(`Mapping test plans to sprints for team ${team.name}...`, baseProgress + 10);
        console.log(`🔗 Mapping test plans to sprints for team ${team.name}...`);
        const planSprintMap = await this._mapTestPlansToSprints(testPlans, sprints);
        
        // Step 3: Filter sprints to last 3 months and calculate coverage. _getIterations returns newest first.
        const filteredSprints = sprints.filter(sprint => {
          if (sprint.attributes?.startDate) {
            return this._isWithinLastThreeMonths(sprint.attributes.startDate);
          } else if (sprint.attributes?.finishDate) {
            return this._isWithinLastThreeMonths(sprint.attributes.finishDate);
          }
          // If no dates available, include recent sprints by position (fallback)
          return sprints.indexOf(sprint) < 6;
        });
        
        const recentSprints = filteredSprints.slice(0, 6); // Take max 6 most recent even after date filtering
        console.log(`📅 After 3-month filter: ${recentSprints.length} sprints for team ${team.name} (from ${sprints.length} total)`);
        
        if (recentSprints.length === 0) {
          console.warn(`⚠️  No recent sprints (last 3 months) found for team ${team.name}`);
          coverageData[team.name] = []; // Empty array, no fake data
          continue;
        }
        
        progressCallback?.(`Calculating coverage for ${recentSprints.length} sprints in team ${team.name}...`, baseProgress + 15);
        console.log(`🎯 Calculating coverage for ${recentSprints.length} most recent sprints (using plan-sprint mapping)`);

        const teamCoverage = await Promise.all(
          recentSprints.map(async (sprint, sprintIndex) => {
            const sprintProgress = baseProgress + 20 + Math.floor((sprintIndex / recentSprints.length) * 30);
            progressCallback?.(`Analyzing sprint ${sprint.name} for team ${team.name}... (${sprintIndex + 1}/${recentSprints.length})`, sprintProgress);
            
            const associatedPlans = planSprintMap.get(sprint.path) || [];
            if (associatedPlans.length === 0) {
              console.log(`   ℹ️  No associated test plans detected for sprint ${sprint.name}; attempting fallback logic later if needed.`);
            }
            const result = await this._calculateSprintTestCoverage(project, team, sprint, associatedPlans);
            if (result['Test Cases Planned'] === 0 && result['Test Cases Executed'] === 0) {
              console.log(`🔎 Zero coverage data point for sprint ${sprint.name} (team ${team.name}).`);
            }
            return result;
          })
        );
        
        // Reverse to show chronological order (most recent first)
  // We want chronological (oldest -> newest) order for charts
  coverageData[team.name] = [...teamCoverage].reverse();
        console.log(`✅ Calculated coverage for team ${team.name}: ${teamCoverage.length} data points`);
        
        // Log summary
        const avgCoverage = teamCoverage.reduce((sum, data) => sum + (data['Functional Test Coverage'] as number), 0) / teamCoverage.length;
        const totalConfigurations = teamCoverage.reduce((sum, data) => sum + (data['Configurations'] as number || 0), 0);
        console.log(`📈 Team ${team.name} summary: ${avgCoverage.toFixed(1)}% avg coverage, ${totalConfigurations} total configurations`);
        
      } catch (error) {
        console.error(`❌ Error calculating test coverage for team ${team.name}:`, error);
        // Return empty data - NO FAKE DATA
        coverageData[team.name] = [];
        console.log(`⚠️  Empty data returned for team ${team.name} due to error`);
      }
    }
    
    progressCallback?.(`Test coverage calculation complete for all teams!`, 100);
    console.log(`\n🎉 Functional test coverage calculation complete for all teams!`);
    console.log(`📊 Final coverage data:`, coverageData);
    return coverageData;
  }

  // Enhanced getTestMetrics to include functional test coverage
  public async getEnhancedTestMetrics(
    project: Project, 
    teams: Team[], 
    progressCallback?: (message: string, progress?: number) => void
  ): Promise<{ 
    effectiveness: Record<string, ChartData[]>, 
    coverage: ChartData[],
    functionalCoverage: Record<string, ChartData[]>
  }> {
    try {
      // Get existing metrics
      const existingMetrics = await this.getTestMetrics(project, teams);
      
      // Get new functional coverage metrics with fallback
      let functionalCoverage: Record<string, ChartData[]> = {};
      
      try {
        functionalCoverage = await this.getFunctionalTestCoverage(project, teams, progressCallback);
      } catch (error) {
        console.warn('Failed to get functional test coverage, using fallback data:', error);
        // Fallback: Create empty data structure
        functionalCoverage = teams.reduce((acc, team) => {
          acc[team.name] = [];
          return acc;
        }, {} as Record<string, ChartData[]>);
      }
      
      return {
        effectiveness: existingMetrics.effectiveness,
        coverage: existingMetrics.coverage,
        functionalCoverage
      };
    } catch (error) {
      console.error('Error in getEnhancedTestMetrics:', error);
      // Return minimal data structure
      const emptyTeamData = teams.reduce((acc, team) => {
        acc[team.name] = [];
        return acc;
      }, {} as Record<string, ChartData[]>);
      
      return {
        effectiveness: emptyTeamData,
        coverage: [],
        functionalCoverage: emptyTeamData
      };
    }
  }
  
  public async getBacklogHealth(project: Project, teams: Team[]): Promise<Record<string, ChartData[]>> {
    const healthData: Record<string, ChartData[]> = {};

    for (const team of teams) {
        const teamAreaPathsInfo = await this._getTeamAreaPathsInfo(project, team);
        const areaPathClause = this._getAreaPathClause(teamAreaPathsInfo);
        
        const ALL_EXCLUDED_STATES = [...COMPLETED_STATES, ...REMOVED_STATES];

        const wiql = `
            SELECT [System.Id]
            FROM WorkItems
            WHERE [System.WorkItemType] = 'User Story'
            AND [System.TeamProject] = @project
            AND [System.State] NOT IN (${ALL_EXCLUDED_STATES.map(s => `'${s}'`).join(',')})
            AND ${areaPathClause}
        `;

        const queryResult = await this._runWiql(project, wiql);
        const workItemIds = queryResult.workItems.map((wi: any) => wi.id);
        const workItems = await this._getWorkItemDetails(project, workItemIds, [STORY_POINTS_FIELD]);

        const storyPoints = workItems.reduce((sum, wi) => sum + (wi.fields[STORY_POINTS_FIELD] || 0), 0);
        
        healthData[team.name] = [{
            name: `${project.name}\\${team.name}`,
            'Count of User Stories': workItems.length,
            'Sum of Story Points': storyPoints
        }];
    }
    
    return healthData;
  }


}