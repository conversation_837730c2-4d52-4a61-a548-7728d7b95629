{"name": "azure-devops-metrics-dashboard", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "serve": "vite preview --host 0.0.0.0 --port 3000", "lint": "tsc --noEmit", "clean": "rm -rf dist", "deploy": "npm run clean && npm ci && npm run build"}, "dependencies": {"react": "^19.1.0", "react-dom": "^19.1.0", "recharts": "^3.1.0"}, "devDependencies": {"@types/node": "^22.14.0", "@types/react": "^19.1.0", "@types/react-dom": "^19.1.0", "@vitejs/plugin-react": "^5.0.2", "autoprefixer": "^10.4.20", "postcss": "^8.5.0", "tailwindcss": "^3.4.17", "terser": "^5.43.1", "typescript": "~5.8.2", "vite": "^6.2.0"}}