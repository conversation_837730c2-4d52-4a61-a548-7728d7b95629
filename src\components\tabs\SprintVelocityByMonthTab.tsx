import React from 'react';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>hart, Bar, XAxis, YAxis, <PERSON>lt<PERSON>, Legend, LabelList } from 'recharts';
import { ChartCard } from '../ChartCard';
import { KpiCard } from '../common/KpiCard';
import { CustomTooltip } from './CustomTooltip';
import type { TabProps, ChartData } from '../../types';
import { CHART_COLORS, KPI_ICONS } from '../../constants';

export const SprintVelocityByMonthTab: React.FC<TabProps> = ({ data, teams }) => {
  const teamHasData = (teamName: string) => data.sprintVelocityByMonth && data.sprintVelocityByMonth[teamName] && data.sprintVelocityByMonth[teamName].length > 0;
  const noDataForTab = !teams.some(team => teamHasData(team.name));

  const KpiIcon: React.FC<{ d: string, className?: string }> = ({ d, className }) => (
    <svg className={`w-8 h-8 ${className}`} fill="none" stroke="currentColor" strokeWidth={1.5} viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
      <path strokeLinecap="round" strokeLinejoin="round" d={d} />
    </svg>
  );
  
  if (noDataForTab) {
    return <div className="p-6 text-center text-slate-400">No Sprint Velocity data available for the selected teams.</div>;
  }
  
  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
        {teams.filter(team => teamHasData(team.name)).map(team => {
            const teamData = data.sprintVelocityByMonth[team.name];
            const avgCompleted = teamData.reduce((acc: number, curr: ChartData) => acc + (curr.Completed as number), 0) / teamData.length;
            const avgCommitted = teamData.reduce((acc: number, curr: ChartData) => acc + (curr.Committed as number), 0) / teamData.length;
            return (
                <KpiCard 
                    key={team.id} 
                    title={`Avg. Monthly Velocity (${team.name})`} 
                    value={`${avgCompleted.toFixed(0)} / ${avgCommitted.toFixed(0)}`} 
                    icon={<KpiIcon d={KPI_ICONS.velocity} />} 
                />
            );
        })}
      </div>
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {teams.map(team => teamHasData(team.name) && (
          <ChartCard key={team.id} title={`Sprint Velocity By Month - ${team.name}`}>
            <ResponsiveContainer width="100%" height="100%">
              <BarChart data={data.sprintVelocityByMonth[team.name]} margin={{ top: 20, right: 30, left: 0, bottom: 5 }}>
                <defs>
                  <linearGradient id="velMonthPrimaryGradient" x1="0" y1="0" x2="0" y2="1">
                    <stop offset="5%" stopColor={CHART_COLORS.primary} stopOpacity={0.9}/>
                    <stop offset="95%" stopColor={CHART_COLORS.accent} stopOpacity={0.6}/>
                  </linearGradient>
                  <linearGradient id="velMonthContrastGradient" x1="0" y1="0" x2="0" y2="1">
                    <stop offset="5%" stopColor={CHART_COLORS.contrastPrimary} stopOpacity={0.7}/>
                    <stop offset="95%" stopColor={CHART_COLORS.contrastAccent} stopOpacity={0.4}/>
                  </linearGradient>
                  <linearGradient id="velMonthDangerGradient" x1="0" y1="0" x2="0" y2="1">
                    <stop offset="5%" stopColor={CHART_COLORS.danger} stopOpacity={0.8}/>
                    <stop offset="95%" stopColor={CHART_COLORS.dangerAccent} stopOpacity={0.5}/>
                  </linearGradient>
                </defs>
                <XAxis dataKey="name" tick={{ fontSize: 12, fill: CHART_COLORS.text }} stroke={CHART_COLORS.grid} />
                <YAxis tick={{ fontSize: 12, fill: CHART_COLORS.text }} stroke={CHART_COLORS.grid} />
                <Tooltip content={<CustomTooltip />} cursor={{fill: 'rgba(14, 165, 233, 0.1)'}} />
                <Legend wrapperStyle={{fontSize: "12px", color: CHART_COLORS.text}}/>
                <Bar dataKey="Completed" fill="url(#velMonthPrimaryGradient)" animationDuration={800}>
                   <LabelList dataKey="Completed" position="top" fontSize={10} fill={CHART_COLORS.text}/>
                </Bar>
                <Bar dataKey="Committed" fill="url(#velMonthContrastGradient)" animationDuration={800} />
                <Bar dataKey="Removed" fill="url(#velMonthDangerGradient)" animationDuration={800} />
              </BarChart>
            </ResponsiveContainer>
          </ChartCard>
        ))}
      </div>
    </div>
  );
};