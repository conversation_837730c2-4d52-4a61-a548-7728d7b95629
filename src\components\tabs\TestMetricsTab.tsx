import React from 'react';
import { <PERSON>sponsive<PERSON>ontainer, BarChart, Bar, XAxis, YAxis, Tooltip, Legend, ReferenceLine, ComposedChart, Line, LabelList } from 'recharts';
import { ChartCard } from '../ChartCard';
import { KpiCard } from '../common/KpiCard';
import { CustomTooltip } from './CustomTooltip';
import type { TabProps, ChartData, Team, KpiStatus } from '../../types';
import { CHART_COLORS, KPI_ICONS } from '../../constants';

const KpiIcon: React.FC<{ d: string, className?: string }> = ({ d, className }) => (
    <svg className={`w-8 h-8 ${className}`} fill="none" stroke="currentColor" strokeWidth={1.5} viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
      <path strokeLinecap="round" strokeLinejoin="round" d={d} />
    </svg>
);

const getEffectivenessKpis = (effectivenessData: Record<string, ChartData[]> | undefined, teams: Team[]) => {
    if (!effectivenessData) return [];
    
    const getStatus = (rate: number): KpiStatus => {
      if (rate >= 98) return 'good';
      if (rate >= 90) return 'neutral';
      return 'warning';
    }

    return teams.filter(team => effectivenessData[team.name] && effectivenessData[team.name].length > 0)
        .map(team => {
            const teamData = effectivenessData[team.name];
            const avgEffectiveness = teamData.reduce((acc, curr) => acc + (curr['Test Effectiveness'] as number), 0) / teamData.length;
            return {
                title: `Avg Test Effectiveness (${team.name})`,
                value: `${avgEffectiveness.toFixed(1)}%`,
                icon: <KpiIcon d={KPI_ICONS.effectiveness} />,
                status: getStatus(avgEffectiveness)
            };
        });
};

const getCoverageKpis = (coverageData: ChartData[] | undefined) => {
    if (!coverageData || coverageData.length === 0) return [];

    const getStatus = (rate: number): KpiStatus => {
        if (rate >= 95) return 'good';
        if (rate >= 90) return 'neutral';
        return 'warning';
    };

    const totalExecuted = coverageData.reduce((acc, curr) => acc + (curr['Test Cases Executed'] as number), 0);
    const totalPlanned = coverageData.reduce((acc, curr) => acc + (curr['Test Cases Planned'] as number), 0);
    const avgCoverage = totalPlanned > 0 ? (totalExecuted / totalPlanned) * 100 : 0;
    
    return [
        {
            title: 'Total Tests Executed (6 Mo)',
            value: totalExecuted,
            icon: <KpiIcon d={KPI_ICONS.coverage} />,
            status: 'neutral' as KpiStatus
        },
        {
            title: 'Avg. Test Coverage (6 Mo)',
            value: `${avgCoverage.toFixed(1)}%`,
            icon: <KpiIcon d={KPI_ICONS.effectiveness} />,
            status: getStatus(avgCoverage)
        }
    ];
};

const getFunctionalCoverageKpis = (functionalCoverageData: Record<string, ChartData[]> | undefined, teams: Team[]) => {
    if (!functionalCoverageData) return [];
    
    const getStatus = (rate: number): KpiStatus => {
      if (rate >= 95) return 'good';
      if (rate >= 90) return 'neutral';
      return 'warning';
    }

    return teams.filter(team => functionalCoverageData[team.name] && functionalCoverageData[team.name].length > 0)
        .map(team => {
            const teamData = functionalCoverageData[team.name];
            const avgCoverage = teamData.reduce((acc, curr) => acc + (curr['Functional Test Coverage'] as number), 0) / teamData.length;
            const totalConfigurations = teamData.reduce((acc, curr) => acc + (curr['Configurations'] as number || 0), 0);
            return [
                {
                    title: `Functional Test Coverage (${team.name})`,
                    value: `${avgCoverage.toFixed(1)}%`,
                    icon: <KpiIcon d={KPI_ICONS.functional || KPI_ICONS.coverage} />,
                    status: getStatus(avgCoverage)
                },
                {
                    title: `Total Configurations (${team.name})`,
                    value: `${totalConfigurations}`,
                    icon: <KpiIcon d={KPI_ICONS.configurations || KPI_ICONS.coverage} />,
                    status: 'neutral' as KpiStatus
                }
            ];
        }).flat();
};


export const TestMetricsTab: React.FC<TabProps> = ({ data, teams, project }) => {
  const effectivenessData = data.testMetrics?.effectiveness;
  const coverageData = data.testMetrics?.coverage;
  const functionalCoverageData = data.testMetrics?.functionalCoverage;
  
  // Add debugging
  console.log('🔍 TestMetricsTab received data:', {
    effectivenessData,
    coverageData,
    functionalCoverageData,
    teams,
    project
  });
  
  console.log('🔍 Functional coverage data keys:', functionalCoverageData ? Object.keys(functionalCoverageData) : 'undefined');
  if (functionalCoverageData) {
    Object.entries(functionalCoverageData).forEach(([teamName, teamData]) => {
      console.log(`🔍 Team ${teamName} data:`, teamData);
    });
  }
  
  const noData = !effectivenessData || (Object.keys(effectivenessData).length === 0 && (!coverageData || coverageData.length === 0) && (!functionalCoverageData || Object.keys(functionalCoverageData).length === 0));

  if (noData) {
    return <div className="p-6 text-center text-slate-400">No Test Metrics data available for the selected teams or project.</div>;
  }
  
  const kpis = [
      ...getEffectivenessKpis(effectivenessData, teams),
      ...getCoverageKpis(coverageData),
      ...getFunctionalCoverageKpis(functionalCoverageData, teams)
  ];

  return (
    <div className="space-y-6">
      {/* KPI Cards */}
      {kpis.length > 0 && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
          {kpis.map((kpi, index) => (
            <KpiCard key={index} {...kpi} />
          ))}
        </div>
      )}

      {/* Test Effectiveness Charts */}
      {effectivenessData && Object.keys(effectivenessData).length > 0 && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {teams.map(team => effectivenessData[team.name] && effectivenessData[team.name].length > 0 && (
            <ChartCard key={team.id} title={`Test Effectiveness - ${team.name}`}>
              <ResponsiveContainer width="100%" height="100%">
                <ComposedChart data={effectivenessData[team.name]} margin={{ top: 20, right: 30, left: 0, bottom: 20 }}>
                  <defs>
                    <linearGradient id="testPrimaryGradient" x1="0" y1="0" x2="0" y2="1">
                      <stop offset="5%" stopColor={CHART_COLORS.primary} stopOpacity={0.9}/>
                      <stop offset="95%" stopColor={CHART_COLORS.accent} stopOpacity={0.6}/>
                    </linearGradient>
                  </defs>
                  <XAxis dataKey="name" tick={{ fontSize: 10, fill: CHART_COLORS.text }} angle={-25} textAnchor="end" height={50} interval={0} stroke={CHART_COLORS.grid}/>
                  <YAxis yAxisId="left" tick={{ fontSize: 12, fill: CHART_COLORS.text }} stroke={CHART_COLORS.grid} />
                  <YAxis yAxisId="right" orientation="right" tick={{ fontSize: 12, fill: CHART_COLORS.text }} stroke={CHART_COLORS.grid} domain={[80, 120]} />
                  <Tooltip content={<CustomTooltip />} />
                  <Legend wrapperStyle={{fontSize: "12px", color: CHART_COLORS.text}}/>
                  <Bar yAxisId="left" dataKey="Testing Defects Count" fill="url(#testPrimaryGradient)" animationDuration={800} />
                  <Line yAxisId="right" type="monotone" dataKey="Test Effectiveness" stroke={CHART_COLORS.danger} dot={false} strokeWidth={2}/>
                </ComposedChart>
              </ResponsiveContainer>
            </ChartCard>
          ))}
        </div>
      )}

      {/* Test Coverage Chart */}
      {coverageData && coverageData.length > 0 && (
        <ChartCard title="Overall Test Coverage">
          <ResponsiveContainer width="100%" height={300}>
            <BarChart data={coverageData} margin={{ top: 20, right: 30, left: 20, bottom: 20 }}>
              <defs>
                <linearGradient id="coverageGradient" x1="0" y1="0" x2="0" y2="1">
                  <stop offset="5%" stopColor={CHART_COLORS.secondary} stopOpacity={0.8}/>
                  <stop offset="95%" stopColor={CHART_COLORS.secondary} stopOpacity={0.2}/>
                </linearGradient>
              </defs>
              <XAxis dataKey="name" className="text-xs" />
              <YAxis className="text-xs" />
              <Tooltip content={<CustomTooltip />} />
              <Bar dataKey="Test Cases Executed" stackId="a" fill={CHART_COLORS.secondary} />
              <Bar dataKey="Test Cases Planned" stackId="b" fill={CHART_COLORS.primary} />
            </BarChart>
          </ResponsiveContainer>
        </ChartCard>
      )}

      {/* Functional Test Coverage Charts */}
      {functionalCoverageData && Object.keys(functionalCoverageData).length > 0 && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <ChartCard title="Test Configuration Count by Team">
            {teams.map(team => functionalCoverageData[team.name] && functionalCoverageData[team.name].length > 0 && (
              <div key={team.id} className="mb-6 last:mb-0">
                <h4 className="text-sm font-medium text-slate-700 mb-3">{team.name}</h4>
                <ResponsiveContainer width="100%" height={250}>
                  <BarChart data={functionalCoverageData[team.name]} margin={{ top: 20, right: 30, left: 10, bottom: 20 }}>
                    <XAxis dataKey="name" className="text-xs" />
                    <YAxis className="text-xs" />
                    <Tooltip content={<CustomTooltip />} />
                    <Bar dataKey="Configurations" fill={CHART_COLORS.warning} radius={[4, 4, 0, 0]}>
                      <LabelList dataKey="Configurations" position="top" className="text-xs" />
                    </Bar>
                  </BarChart>
                </ResponsiveContainer>
              </div>
            ))}
          </ChartCard>
        </div>
      )}

      {/* Test Results Breakdown Charts */}
      {functionalCoverageData && Object.keys(functionalCoverageData).length > 0 && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <ChartCard title="Test Cases & Functional Coverage">
            {teams.map(team => functionalCoverageData[team.name] && functionalCoverageData[team.name].length > 0 && (
              <div key={team.id} className="mb-6 last:mb-0">
                <h4 className="text-sm font-medium text-slate-700 mb-3">{team.name}</h4>
                <ResponsiveContainer width="100%" height={250}>
                  <ComposedChart data={functionalCoverageData[team.name]} margin={{ top: 20, right: 30, left: 10, bottom: 20 }}>
                    <defs>
                      <linearGradient id={`plannedGradient-${team.id}`} x1="0" y1="0" x2="0" y2="1">
                        <stop offset="5%" stopColor={CHART_COLORS.secondary} stopOpacity={0.8}/>
                        <stop offset="95%" stopColor={CHART_COLORS.secondary} stopOpacity={0.4}/>
                      </linearGradient>
                      <linearGradient id={`executedGradient-${team.id}`} x1="0" y1="0" x2="0" y2="1">
                        <stop offset="5%" stopColor={CHART_COLORS.primary} stopOpacity={0.8}/>
                        <stop offset="95%" stopColor={CHART_COLORS.primary} stopOpacity={0.4}/>
                      </linearGradient>
                    </defs>
                    <XAxis dataKey="name" className="text-xs" />
                    <YAxis yAxisId="left" tick={{ fontSize: 12, fill: CHART_COLORS.text }} stroke={CHART_COLORS.grid} />
                    <YAxis yAxisId="right" orientation="right" tick={{ fontSize: 12, fill: CHART_COLORS.text }} stroke={CHART_COLORS.grid} domain={[0, 100]} />
                    <Tooltip content={<CustomTooltip />} />
                    <Legend wrapperStyle={{fontSize: "12px", color: CHART_COLORS.text}}/>
                    <Bar yAxisId="left" dataKey="Test Cases Planned" fill={`url(#plannedGradient-${team.id})`} radius={[4, 4, 0, 0]} />
                    <Bar yAxisId="left" dataKey="Test Cases Executed" fill={`url(#executedGradient-${team.id})`} radius={[4, 4, 0, 0]} />
                    <Line yAxisId="right" type="monotone" dataKey="Functional Test Coverage" stroke={CHART_COLORS.good} strokeWidth={3} dot={{ fill: CHART_COLORS.good, strokeWidth: 2, r: 4 }} />
                    <ReferenceLine yAxisId="right" y={95} stroke={CHART_COLORS.good} strokeDasharray="3 3" />
                  </ComposedChart>
                </ResponsiveContainer>
              </div>
            ))}
          </ChartCard>

          <ChartCard title="Test Results Breakdown">
            {teams.map(team => functionalCoverageData[team.name] && functionalCoverageData[team.name].length > 0 && (
              <div key={team.id} className="mb-6 last:mb-0">
                <h4 className="text-sm font-medium text-slate-700 mb-3">{team.name}</h4>
                <ResponsiveContainer width="100%" height={250}>
                  <BarChart data={functionalCoverageData[team.name]} margin={{ top: 20, right: 30, left: 10, bottom: 20 }}>
                    <XAxis dataKey="name" className="text-xs" />
                    <YAxis className="text-xs" />
                    <Tooltip content={<CustomTooltip />} />
                    <Bar dataKey="Tests Passed" stackId="results" fill={CHART_COLORS.good} />
                    <Bar dataKey="Tests Failed" stackId="results" fill={CHART_COLORS.danger} />
                    <Bar dataKey="Tests Blocked" stackId="results" fill={CHART_COLORS.warning} />
                    <Bar dataKey="Tests Not Run" stackId="results" fill={CHART_COLORS.secondary} />
                  </BarChart>
                </ResponsiveContainer>
              </div>
            ))}
          </ChartCard>
        </div>
      )}
    </div>
  );
};